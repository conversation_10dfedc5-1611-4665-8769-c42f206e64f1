import 'package:qsr_app/screens/menu_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:qsr_app/screens/checkout_screen.dart';
import 'package:qsr_app/services/cart_service.dart';
import '../constants/typography.dart';
import '../constants/colors.dart';
import '../widgets/cart_item_edit_dialog.dart';
import '../widgets/heat_level_dialog.dart';
import '../models/menu_item.dart';
import '../services/language_service.dart';

class CartScreen extends StatefulWidget {
  final CartService cartService;

  const CartScreen({super.key, required this.cartService});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {

  /// ✅ Show heat level editing dialog
  void _showHeatLevelDialog(cartItem, String itemName) {
    if (!widget.cartService.canEditHeatLevel(cartItem)) return;

    showDialog(
      context: context,
      builder: (context) => HeatLevelDialog(
        currentHeatLevel: widget.cartService.getCurrentHeatLevel(cartItem),
        itemName: itemName,
        onHeatLevelSelected: (newHeatLevel) {
          widget.cartService.updateHeatLevel(cartItem, newHeatLevel);
          setState(() {});
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Heat level updated to $newHeatLevel'),
              backgroundColor: Colors.green,
            ),
          );
        },
      ),
    );
  }

  void _showEditDialog(BuildContext context, cartItem, int index) {
    showDialog(
      context: context,
      builder: (context) => CartItemEditDialog(
        cartItem: cartItem,
        itemIndex: index,
        cartService: widget.cartService,
        onUpdated: () => setState(() {}),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, cartItem, int index) {
    showDialog(
      context: context,
      builder: (context) => Consumer<LanguageService>(
        builder: (context, languageService, child) {
          return AlertDialog(
            title: Text(languageService.getTranslatedText('DELETE ITEM')),
            content: Text('${languageService.getTranslatedText('Remove from cart?')} ${cartItem.menuItem.name}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(languageService.getTranslatedText('CANCEL')),
              ),
          ElevatedButton(
            onPressed: () {
              widget.cartService.removeCartItemByIndex(index);
              setState(() {});
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${cartItem.menuItem.name} removed from cart'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(languageService.getTranslatedText('DELETE')),
          ),
        ],
      );
        },
      ),
    );
  }

  void _showClearCartDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('CLEAR CART'),
        content: const Text('Remove all items from cart?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.cartService.clearCart();
              setState(() {});
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cart cleared'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('CLEAR ALL'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final subtotal = widget.cartService.cart.items.fold<double>(
      0,
      (sum, item) => sum + (item.itemPrice * item.quantity),
    );

    // Canadian taxes: 5% GST + 7% PST = 12% total
    final gst = subtotal * 0.05;
    final pst = subtotal * 0.07;
    final total = subtotal + gst + pst;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Cart',
          style: AppTypography.displaySmall.copyWith(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white // White text for dark mode
                : const Color.fromARGB(255, 122, 59, 0), // Brown for light mode
          ),
        ).animate()
          .fadeIn(duration: const Duration(milliseconds: 600))
          .slideX(begin: -0.2, end: 0),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withAlpha(0),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withAlpha((255 * 0.5).round()),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: () => Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => MenuScreen(
                cartService: widget.cartService,
              ),
            ),
          ),
        ),
        actions: widget.cartService.cart.items.isNotEmpty
            ? [
                IconButton(
                  onPressed: _showClearCartDialog,
                  icon: const Icon(Icons.clear_all, color: Colors.white),
                  tooltip: 'Clear Cart',
                ).animate().fadeIn().scale(delay: const Duration(milliseconds: 300)),
              ]
            : null,
      ),
      body: Column(
        children: [
          Expanded(
            child: widget.cartService.cart.items.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.shopping_cart_outlined, size: 100)
                            .animate()
                            .scale(
                              duration: const Duration(milliseconds: 600),
                              curve: Curves.easeOutBack,
                            ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                builder: (context) => MenuScreen(
                                  cartService: widget.cartService,
                                ),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 28),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: const Text(
                            'START ORDERING',
                            style: TextStyle(
                              fontFamily: 'MontserratBlack',
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ).animate().fadeIn().slideY(begin: 0.2),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: widget.cartService.cart.items.length,
                    itemBuilder: (context, index) {
                      final cartItem = widget.cartService.cart.items[index];
                      return Dismissible(
                        key: Key('cart_item_$index'),
                        direction: DismissDirection.endToStart,
                        background: Container(
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.only(right: 20),
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Consumer<LanguageService>(
                            builder: (context, languageService, child) {
                              return Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  const Icon(Icons.delete, color: Colors.white, size: 24),
                                  const SizedBox(width: 8),
                                  Text(
                                    languageService.getTranslatedText('DELETE'),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                        confirmDismiss: (direction) async {
                          return await showDialog<bool>(
                            context: context,
                            builder: (context) => Consumer<LanguageService>(
                              builder: (context, languageService, child) {
                                return AlertDialog(
                                  title: Text(languageService.getTranslatedText('DELETE ITEM')),
                                  content: Text('${languageService.getTranslatedText('Remove from cart?')} ${cartItem.menuItem.name}'),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.of(context).pop(false),
                                      child: Text(languageService.getTranslatedText('CANCEL')),
                                    ),
                                    ElevatedButton(
                                      onPressed: () => Navigator.of(context).pop(true),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                        foregroundColor: Colors.white,
                                      ),
                                      child: Text(languageService.getTranslatedText('DELETE')),
                                    ),
                                  ],
                                );
                              },
                            ),
                          );
                        },
                        onDismissed: (direction) {
                          widget.cartService.removeCartItemByIndex(index);
                          setState(() {});
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('${cartItem.menuItem.name} removed from cart'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        },
                        child: Card(
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                              // Main item info
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      cartItem.displayName,
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? Colors.white // White text for dark mode
                                            : Colors.black87, // Dark text for light mode
                                      ),
                                    ),
                                  ),
                                  // Quantity controls
                                  Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? const Color(0xFF2d4a4a) // Dark teal border for dark mode
                                            : Colors.grey.shade300, // Light grey border for light mode
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? const Color(0xFF1a3d3d) // Dark teal background for dark mode
                                          : Colors.white, // White background for light mode
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        IconButton(
                                          onPressed: cartItem.quantity > 1
                                              ? () {
                                                  widget.cartService.updateCartItemQuantity(
                                                    index,
                                                    cartItem.quantity - 1,
                                                  );
                                                  setState(() {});
                                                }
                                              : null,
                                          icon: const Icon(Icons.remove, size: 16),
                                          constraints: const BoxConstraints(
                                            minWidth: 32,
                                            minHeight: 32,
                                          ),
                                          padding: EdgeInsets.zero,
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 12),
                                          child: Text(
                                            '${cartItem.quantity}',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.white // White text for dark mode
                                                  : Colors.black87, // Dark text for light mode
                                            ),
                                          ),
                                        ),
                                        IconButton(
                                          onPressed: () {
                                            widget.cartService.updateCartItemQuantity(
                                              index,
                                              cartItem.quantity + 1,
                                            );
                                            setState(() {});
                                          },
                                          icon: const Icon(Icons.add, size: 16),
                                          constraints: const BoxConstraints(
                                            minWidth: 32,
                                            minHeight: 32,
                                          ),
                                          padding: EdgeInsets.zero,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),

                              // Item description
                              Text(
                                cartItem.menuItem.description,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.grey[400] // Light grey for dark mode
                                      : Colors.grey, // Grey for light mode
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),

                              // Combo Meal Details
                              if (cartItem.isCombo) ...[
                                const SizedBox(height: 12),
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? const Color(0xFF1a3d3d).withValues(alpha: 0.8) // Dark teal with transparency for dark mode
                                        : Colors.green[50], // Light green for light mode
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Colors.green.withValues(alpha: 0.5) // Green border with transparency for dark mode
                                          : Colors.green[200]!, // Light green border for light mode
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(Icons.local_offer, color: Colors.green[600], size: 16),
                                          const SizedBox(width: 6),
                                          Text(
                                            'COMBO MEAL',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.green[300] // Lighter green for dark mode
                                                  : Colors.green, // Green for light mode
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Main: ${cartItem.comboMeal!.mainItem.name}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Theme.of(context).brightness == Brightness.dark
                                              ? Colors.white // White text for dark mode
                                              : Colors.black87, // Dark text for light mode
                                        ),
                                      ),
                                      // Show bun selection for sandwiches in combos
                                      if (cartItem.comboMeal!.mainItem.category.toLowerCase().contains('sandwich') &&
                                          cartItem.comboMeal!.mainItem.selectedBunType != null)
                                        Text(
                                          'Bun: ${cartItem.comboMeal!.mainItem.selectedBunType}',
                                          style: TextStyle(
                                            fontSize: 11,
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? Colors.grey[400] // Light grey for dark mode
                                                : Colors.grey, // Grey for light mode
                                          ),
                                        ),
                                      if (cartItem.comboMeal!.selectedDrink != null)
                                        Text(
                                          'Drink: ${cartItem.comboMeal!.selectedDrink!.name}${cartItem.comboMeal!.selectedDrinkSize != null ? ' (${cartItem.comboMeal!.selectedDrinkSize})' : ''}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? Colors.white // White text for dark mode
                                                : Colors.black87, // Dark text for light mode
                                          ),
                                        ),
                                      if (cartItem.comboMeal!.selectedSide != null)
                                        Text(
                                          'Side: ${cartItem.comboMeal!.selectedSide!.name}${cartItem.comboMeal!.selectedSideSize != null ? ' (${cartItem.comboMeal!.selectedSideSize})' : ''}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? Colors.white // White text for dark mode
                                                : Colors.black87, // Dark text for light mode
                                          ),
                                        ),
                                      if (cartItem.comboMeal!.savings > 0) ...[
                                        const SizedBox(height: 4),
                                        Text(
                                          'You Save: \$${cartItem.comboMeal!.savings.toStringAsFixed(2)}',
                                          style: TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.bold,
                                            color: Theme.of(context).brightness == Brightness.dark
                                                ? Colors.green[300] // Lighter green for dark mode
                                                : Colors.green, // Green for light mode
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],

                              // Customizations and Extras (show for all items including combos)
                              if (cartItem.menuItem.selectedSauces?.isNotEmpty == true ||
                                  (cartItem.comboMeal == null && cartItem.menuItem.selectedBunType != null) ||
                                  (cartItem.comboMeal == null && cartItem.menuItem.selectedHeatLevel != null) ||
                                  (cartItem.comboMeal?.mainItem.selectedHeatLevel != null) ||
                                  (cartItem.comboMeal?.mainItem.selectedBunType != null) ||
                                  cartItem.selectedSize != null ||
                                  cartItem.crewPackCustomization != null ||
                                  cartItem.customizations != null ||
                                  cartItem.extras != null) ...[
                                const SizedBox(height: 12),
                                const Text(
                                  'Customizations:',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.deepOrange,
                                  ),
                                ),
                                const SizedBox(height: 8),

                                // Selected sauces
                                if (cartItem.menuItem.selectedSauces?.isNotEmpty == true)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      color: Colors.orange[50],
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.orange[200]!),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Consumer<LanguageService>(
                                          builder: (context, languageService, child) {
                                            return Row(
                                              children: [
                                                const Icon(Icons.water_drop, size: 14, color: Colors.orange),
                                                const SizedBox(width: 6),
                                                Text(
                                                  languageService.getTranslatedText('Selected Sauces:'),
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: 12,
                                                    color: Colors.orange,
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                        const SizedBox(height: 6),
                                        Wrap(
                                          spacing: 6,
                                          runSpacing: 4,
                                          children: cartItem.menuItem.selectedSauces!.map<Widget>((sauce) {
                                            return Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                              decoration: BoxDecoration(
                                                color: Colors.orange[100],
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                              child: Text(
                                                sauce,
                                                style: const TextStyle(
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w500,
                                                  color: Colors.orange,
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                        ),
                                      ],
                                    ),
                                  ),

                                // Selected bun type (only for non-combo items)
                                if (cartItem.menuItem.selectedBunType != null && cartItem.comboMeal == null)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.green[50],
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(color: Colors.green[200]!),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(Icons.bakery_dining, size: 12, color: Colors.green),
                                        const SizedBox(width: 6),
                                        Text(
                                          'Bun: ${cartItem.menuItem.selectedBunType}',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.green,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                // Selected bun type for combo items
                                if (cartItem.comboMeal?.mainItem.selectedBunType != null)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.green[50],
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(color: Colors.green[200]!),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(Icons.bakery_dining, size: 12, color: Colors.green),
                                        const SizedBox(width: 6),
                                        Text(
                                          'Bun: ${cartItem.comboMeal!.mainItem.selectedBunType}',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.green,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                // Selected heat level (only for non-combo items)
                                if (cartItem.menuItem.selectedHeatLevel != null && cartItem.comboMeal == null)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.red[50],
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(color: Colors.red[200]!),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(Icons.whatshot, size: 12, color: Colors.red),
                                        const SizedBox(width: 6),
                                        Text(
                                          'Heat: ${cartItem.menuItem.selectedHeatLevel}',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.red,
                                          ),
                                        ),
                                        // ✅ Edit heat level button
                                        if (widget.cartService.canEditHeatLevel(cartItem)) ...[
                                          const SizedBox(width: 8),
                                          GestureDetector(
                                            onTap: () => _showHeatLevelDialog(cartItem, cartItem.displayName),
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                              decoration: BoxDecoration(
                                                color: Colors.red[100],
                                                borderRadius: BorderRadius.circular(4),
                                              ),
                                              child: const Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(Icons.edit, size: 10, color: Colors.red),
                                                  SizedBox(width: 2),
                                                  Text(
                                                    'EDIT',
                                                    style: TextStyle(
                                                      fontSize: 9,
                                                      fontWeight: FontWeight.bold,
                                                      color: Colors.red,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),

                                // Selected heat level for combo items
                                if (cartItem.comboMeal?.mainItem.selectedHeatLevel != null)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.red[50],
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(color: Colors.red[200]!),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(Icons.whatshot, size: 12, color: Colors.red),
                                        const SizedBox(width: 6),
                                        Text(
                                          'Heat: ${cartItem.comboMeal!.mainItem.selectedHeatLevel}',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.red,
                                          ),
                                        ),
                                        // ✅ Edit heat level button for combo items
                                        if (widget.cartService.canEditHeatLevel(cartItem)) ...[
                                          const SizedBox(width: 8),
                                          GestureDetector(
                                            onTap: () => _showHeatLevelDialog(cartItem, '${cartItem.displayName} (Main Item)'),
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                              decoration: BoxDecoration(
                                                color: Colors.red[100],
                                                borderRadius: BorderRadius.circular(4),
                                              ),
                                              child: const Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(Icons.edit, size: 10, color: Colors.red),
                                                  SizedBox(width: 2),
                                                  Text(
                                                    'EDIT',
                                                    style: TextStyle(
                                                      fontSize: 9,
                                                      fontWeight: FontWeight.bold,
                                                      color: Colors.red,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),

                                // Selected size/bun
                                if (cartItem.selectedSize != null)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.blue[50],
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(color: Colors.blue[200]!),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          cartItem.menuItem.category.toLowerCase() == 'sandwiches'
                                            ? Icons.bakery_dining
                                            : Icons.straighten,
                                          size: 12,
                                          color: Colors.blue
                                        ),
                                        const SizedBox(width: 6),
                                        Text(
                                          cartItem.menuItem.category.toLowerCase() == 'sandwiches'
                                            ? 'Bun: ${cartItem.selectedSize}'
                                            : 'Size: ${cartItem.selectedSize}',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.blue,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                // Display extras
                                if (cartItem.extras != null && cartItem.extras!.totalExtrasCount > 0)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFFF6B35).withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: const Color(0xFFFF6B35).withValues(alpha: 0.3)),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            const Icon(Icons.add_circle, size: 14, color: Color(0xFFFF6B35)),
                                            const SizedBox(width: 6),
                                            const Text(
                                              'Extras:',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w600,
                                                color: Color(0xFFFF6B35),
                                              ),
                                            ),
                                            const Spacer(),
                                            Text(
                                              '+\$${cartItem.extras!.totalExtrasPrice.toStringAsFixed(2)}',
                                              style: const TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFFFF6B35),
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        ...cartItem.extras!.selectedExtras.entries.expand((entry) {
                                          return entry.value.map((selectedExtra) {
                                            return Padding(
                                              padding: const EdgeInsets.only(bottom: 4),
                                              child: Row(
                                                children: [
                                                  Container(
                                                    width: 16,
                                                    height: 16,
                                                    decoration: BoxDecoration(
                                                      color: const Color(0xFFFF6B35),
                                                      borderRadius: BorderRadius.circular(8),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        '${selectedExtra.quantity}',
                                                        style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 10,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: Text(
                                                      selectedExtra.extra.name,
                                                      style: const TextStyle(
                                                        fontSize: 11,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                  Text(
                                                    '+\$${selectedExtra.totalPrice.toStringAsFixed(2)}',
                                                    style: const TextStyle(
                                                      fontSize: 10,
                                                      color: Color(0xFFFF6B35),
                                                      fontWeight: FontWeight.w600,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                          });
                                        }),
                                        if (cartItem.extras!.specialInstructions?.isNotEmpty == true) ...[
                                          const SizedBox(height: 8),
                                          const Divider(height: 1),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [
                                              const Icon(Icons.note, size: 12, color: Colors.grey),
                                              const SizedBox(width: 6),
                                              Expanded(
                                                child: Text(
                                                  'Note: ${cartItem.extras!.specialInstructions}',
                                                  style: const TextStyle(
                                                    fontSize: 11,
                                                    color: Colors.grey,
                                                    fontStyle: FontStyle.italic,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),

                                // Display crew pack customizations (both sandwich selections and other items)
                                if (cartItem.crewPackCustomization != null || cartItem.customizations != null)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.deepOrange.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.deepOrange.withValues(alpha: 0.3)),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            const Icon(Icons.group, size: 14, color: Colors.deepOrange),
                                            const SizedBox(width: 6),
                                            const Text(
                                              'Crew Pack Selections:',
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.deepOrange,
                                              ),
                                            ),
                                            const Spacer(),
                                            Text(
                                              '${_getTotalCrewPackItems(cartItem)} items',
                                              style: const TextStyle(
                                                fontSize: 11,
                                                color: Colors.deepOrange,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),

                                        // Display sandwich selections (from crewPackCustomization)
                                        if (cartItem.crewPackCustomization != null)
                                          ...widget.cartService.getCrewPackSelectionDetailsSync(cartItem.crewPackCustomization!).map<Widget>((selectionDetail) {
                                            return Padding(
                                              padding: const EdgeInsets.only(bottom: 6),
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  const Icon(Icons.restaurant, size: 12, color: Colors.grey),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Text(
                                                          selectionDetail.displayText,
                                                          style: const TextStyle(
                                                            fontSize: 11,
                                                            fontWeight: FontWeight.w500,
                                                          ),
                                                        ),
                                                        if (selectionDetail.menuItem.description.isNotEmpty)
                                                          Text(
                                                            selectionDetail.menuItem.description,
                                                            style: const TextStyle(
                                                              fontSize: 9,
                                                              color: Colors.grey,
                                                              fontStyle: FontStyle.italic,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  if (selectionDetail.priceDisplayText.isNotEmpty)
                                                    Text(
                                                      selectionDetail.priceDisplayText,
                                                      style: const TextStyle(
                                                        fontSize: 10,
                                                        color: Colors.green,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            );
                                          }),

                                        // Display other crew pack items (from customizations)
                                        if (cartItem.customizations != null)
                                          ...cartItem.customizations!.entries.expand((entry) {
                                            final category = entry.key;
                                            final items = entry.value;
                                            return items.map<Widget>((item) {
                                              return Padding(
                                                padding: const EdgeInsets.only(bottom: 6),
                                                child: Row(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Icon(_getCategoryIcon(category), size: 12, color: Colors.grey),
                                                    const SizedBox(width: 8),
                                                    Expanded(
                                                      child: Text(
                                                        item.name,
                                                        style: const TextStyle(
                                                          fontSize: 11,
                                                          fontWeight: FontWeight.w500,
                                                        ),
                                                      ),
                                                    ),
                                                    Text(
                                                      category,
                                                      style: const TextStyle(
                                                        fontSize: 9,
                                                        color: Colors.grey,
                                                        fontStyle: FontStyle.italic,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            });
                                          }),

                                        // Display dynamic customizations (items with heat levels, etc.)
                                        if (cartItem.dynamicCustomizations != null)
                                          ...cartItem.dynamicCustomizations!.entries.expand((entry) {
                                            final category = entry.key;
                                            final items = entry.value;
                                            return items.map<Widget>((item) {
                                              if (item is Map<String, dynamic> && item.containsKey('item')) {
                                                final menuItem = item['item'] as MenuItem;
                                                final heatLevel = item['heatLevel'] as String?;
                                                return Padding(
                                                  padding: const EdgeInsets.only(bottom: 6),
                                                  child: Row(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Icon(_getCategoryIcon(category), size: 12, color: Colors.grey),
                                                      const SizedBox(width: 8),
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          children: [
                                                            Text(
                                                              menuItem.name,
                                                              style: const TextStyle(
                                                                fontSize: 11,
                                                                fontWeight: FontWeight.w500,
                                                              ),
                                                            ),
                                                            if (heatLevel != null && heatLevel.isNotEmpty && heatLevel != 'Plain')
                                                              Text(
                                                                'Heat Level: $heatLevel',
                                                                style: const TextStyle(
                                                                  fontSize: 9,
                                                                  color: Colors.red,
                                                                  fontWeight: FontWeight.w500,
                                                                ),
                                                              ),
                                                          ],
                                                        ),
                                                      ),
                                                      Text(
                                                        category,
                                                        style: const TextStyle(
                                                          fontSize: 9,
                                                          color: Colors.grey,
                                                          fontStyle: FontStyle.italic,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              } else {
                                                return const SizedBox.shrink();
                                              }
                                            });
                                          }),
                                      ],
                                    ),
                                  ),
                              ],

                                const SizedBox(height: 12),
                                // Price and Actions
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          'Total:',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        Text(
                                          '\$${(cartItem.itemPrice * cartItem.quantity).toStringAsFixed(2)}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 18,
                                            color: Colors.deepOrange,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        // Edit button
                                        IconButton(
                                          onPressed: () => _showEditDialog(context, cartItem, index),
                                          icon: const Icon(Icons.edit),
                                          style: IconButton.styleFrom(
                                            backgroundColor: Colors.blue.withValues(alpha: 0.1),
                                            foregroundColor: Colors.blue,
                                          ),
                                          tooltip: 'Edit Item',
                                        ),
                                        const SizedBox(width: 8),
                                        // Delete button
                                        IconButton(
                                          onPressed: () => _showDeleteDialog(context, cartItem, index),
                                          icon: const Icon(Icons.delete),
                                          style: IconButton.styleFrom(
                                            backgroundColor: Colors.red.withValues(alpha: 0.1),
                                            foregroundColor: Colors.red,
                                          ),
                                          tooltip: 'Delete Item',
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
          if (widget.cartService.cart.items.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Subtotal
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Subtotal:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white // White text for dark mode
                              : Colors.black87, // Dark text for light mode
                        ),
                      ),
                      Text(
                        '\$${subtotal.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white // White text for dark mode
                              : Colors.black87, // Dark text for light mode
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // GST
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'GST (5%):',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[400] // Light grey for dark mode
                              : Colors.grey, // Grey for light mode
                        ),
                      ),
                      Text(
                        '\$${gst.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[400] // Light grey for dark mode
                              : Colors.grey, // Grey for light mode
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  // PST
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'PST (7%):',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[400] // Light grey for dark mode
                              : Colors.grey, // Grey for light mode
                        ),
                      ),
                      Text(
                        '\$${pst.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey[400] // Light grey for dark mode
                              : Colors.grey, // Grey for light mode
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Divider(),
                  const SizedBox(height: 8),
                  // Total
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total:',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white // White text for dark mode
                              : Colors.black87, // Dark text for light mode
                        ),
                      ),
                      Text(
                        '\$${total.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFFDC2626) // Red accent for dark mode
                              : Colors.deepOrange, // Orange for light mode
                        ),
                      ),
                    ],
                  ).animate().slideY(begin: 0.2).fadeIn(),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => CheckoutScreen(
                          cart: widget.cartService.cart,
                        ),
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepOrange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(                          'CHECKOUT',                          style: TextStyle(
                            fontFamily: 'MontserratBlack',
                            fontSize: 18,
                            height: 1.42,
                            letterSpacing: 0.1,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(Icons.arrow_forward, color: Colors.white),
                      ],
                    ),
                  ).animate().scale().fadeIn(delay: const Duration(milliseconds: 200)),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// 🔢 Get total number of items in crew pack (both sandwich selections and other customizations)
  int _getTotalCrewPackItems(dynamic cartItem) {
    int total = 0;

    // Count sandwich selections
    if (cartItem.crewPackCustomization != null) {
      total += cartItem.crewPackCustomization!.selections.length as int;
    }

    // Count other customizations
    if (cartItem.customizations != null) {
      for (var entry in cartItem.customizations!.entries) {
        total += entry.value.length as int;
      }
    }

    // Count dynamic customizations
    if (cartItem.dynamicCustomizations != null) {
      for (var entry in cartItem.dynamicCustomizations!.entries) {
        total += entry.value.length as int;
      }
    }

    return total;
  }

  /// 🎯 Get appropriate icon for different crew pack categories
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'sandwiches':
        return Icons.restaurant;
      case 'chicken bites':
        return Icons.fastfood;
      case 'whole wings':
        return Icons.local_dining;
      case 'chicken pieces (bone-in)':
        return Icons.set_meal;
      case 'sides':
        return Icons.rice_bowl;
      case 'sauces':
        return Icons.water_drop;
      case 'beverages':
        return Icons.local_drink;
      case 'pickles/pickled jalapeños':
        return Icons.eco;
      default:
        return Icons.restaurant_menu;
    }
  }
}

class CartItemCard extends StatefulWidget {
  final dynamic cartItem;
  final int index;
  final Function(DismissDirection) onDismissed;
  final CartService cartService;

  const CartItemCard({
    super.key,
    required this.cartItem,
    required this.index,
    required this.onDismissed,
    required this.cartService,
  });

  @override
  State<CartItemCard> createState() => _CartItemCardState();
}

class _CartItemCardState extends State<CartItemCard>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _scaleAnimation;
  late final Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    final curve = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(curve);

    _elevationAnimation = Tween<double>(
      begin: 1,
      end: 6,
    ).animate(curve);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleHoverChanged(bool isHovered) {
    if (!mounted) return;
    setState(() {
      if (isHovered) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(widget.cartItem.menuItem.name + widget.index.toString()),
      direction: DismissDirection.endToStart,
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16.0),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      onDismissed: widget.onDismissed,
      child: Listener(
        onPointerDown: (_) => _handleHoverChanged(true),
        onPointerUp: (_) => _handleHoverChanged(false),
        onPointerCancel: (_) => _handleHoverChanged(false),
        child: MouseRegion(
          onEnter: (_) => _handleHoverChanged(true),
          onExit: (_) => _handleHoverChanged(false),
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) => Transform.scale(
              scale: _scaleAnimation.value,
              child: Card(
                margin: const EdgeInsets.all(8.0),
                elevation: _elevationAnimation.value,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.cartItem.menuItem.name,
                                  style: const TextStyle(
                                    fontSize: 18.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                // Display selected sauces
                                if (widget.cartItem.menuItem.selectedSauces?.isNotEmpty ?? false)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Container(
                                      padding: const EdgeInsets.all(10.0),
                                      decoration: BoxDecoration(
                                        color: Colors.orange[50],
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.orange[200]!),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Consumer<LanguageService>(
                                            builder: (context, languageService, child) {
                                              return Row(
                                                children: [
                                                  const Icon(Icons.water_drop, size: 14, color: Colors.orange),
                                                  const SizedBox(width: 6),
                                                  Text(
                                                    languageService.getTranslatedText('Selected Sauces:'),
                                                    style: const TextStyle(
                                                      fontWeight: FontWeight.w600,
                                                      fontSize: 12,
                                                      color: Colors.orange,
                                                    ),
                                                  ),
                                                ],
                                              );
                                            },
                                          ),
                                          const SizedBox(height: 6),
                                          Wrap(
                                            spacing: 6,
                                            runSpacing: 4,
                                            children: widget.cartItem.menuItem.selectedSauces!.map<Widget>((sauce) {
                                              return Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                decoration: BoxDecoration(
                                                  color: Colors.orange[100],
                                                  borderRadius: BorderRadius.circular(12),
                                                ),
                                                child: Text(
                                                  sauce,
                                                  style: const TextStyle(
                                                    fontSize: 11,
                                                    fontWeight: FontWeight.w500,
                                                    color: Colors.orange,
                                                  ),
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                // Display bun type selection
                                if (widget.cartItem.menuItem.selectedBunType != null)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: Colors.green[50],
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(color: Colors.green[200]!),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(Icons.bakery_dining, size: 12, color: Colors.green),
                                          const SizedBox(width: 6),
                                          Text(
                                            'Bun: ${widget.cartItem.menuItem.selectedBunType}',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.green,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                // Display heat level selection
                                if (widget.cartItem.menuItem.selectedHeatLevel != null)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: Colors.red[50],
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(color: Colors.red[200]!),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(Icons.whatshot, size: 12, color: Colors.red),
                                          const SizedBox(width: 6),
                                          Text(
                                            'Heat Level: ${widget.cartItem.menuItem.selectedHeatLevel}',
                                            style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.red,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                // Display crew pack customizations
                                if (widget.cartItem.crewPackCustomization != null)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Container(
                                      padding: const EdgeInsets.all(12.0),
                                      decoration: BoxDecoration(
                                        color: Colors.grey[50],
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.grey[200]!),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            'Crew Pack Selections:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 13,
                                              color: Colors.deepOrange,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          ...widget.cartService.getCrewPackSelectionDetailsSync(widget.cartItem.crewPackCustomization!).map<Widget>((selectionDetail) {
                                            return Padding(
                                              padding: const EdgeInsets.only(bottom: 6.0),
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  const Icon(Icons.restaurant, size: 14, color: Colors.grey),
                                                  const SizedBox(width: 6),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Text(
                                                          selectionDetail.displayText,
                                                          style: const TextStyle(
                                                            fontSize: 12,
                                                            fontWeight: FontWeight.w500,
                                                          ),
                                                        ),
                                                        if (selectionDetail.menuItem.description.isNotEmpty)
                                                          Text(
                                                            selectionDetail.menuItem.description,
                                                            style: const TextStyle(
                                                              fontSize: 10,
                                                              color: Colors.grey,
                                                              fontStyle: FontStyle.italic,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  if (selectionDetail.priceDisplayText.isNotEmpty)
                                                    Text(
                                                      selectionDetail.priceDisplayText,
                                                      style: const TextStyle(
                                                        fontSize: 11,
                                                        color: Colors.green,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            );
                                          }),
                                        ],
                                      ),
                                    ),
                                  ),
                                // Display regular customizations
                                if (widget.cartItem.customizations != null)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Container(
                                      padding: const EdgeInsets.all(12.0),
                                      decoration: BoxDecoration(
                                        color: Colors.blue[50],
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.blue[200]!),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            'Pack Includes:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 13,
                                              color: Colors.blue,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          ...widget.cartItem.customizations!.entries.map<Widget>((entry) {
                                            return Padding(
                                              padding: const EdgeInsets.only(bottom: 6.0),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    '${entry.value.length}x ${entry.key}:',
                                                    style: const TextStyle(
                                                      fontSize: 12,
                                                      fontWeight: FontWeight.w600,
                                                      color: Colors.blue,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 2),
                                                  ...entry.value.map<Widget>((item) {
                                                    return Padding(
                                                      padding: const EdgeInsets.only(left: 12.0, bottom: 2.0),
                                                      child: Row(
                                                        children: [
                                                          const Icon(Icons.arrow_right, size: 12, color: Colors.grey),
                                                          const SizedBox(width: 4),
                                                          Expanded(
                                                            child: Text(
                                                              item.name,
                                                              style: const TextStyle(
                                                                fontSize: 11,
                                                                color: Colors.grey,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  }).toList(),
                                                ],
                                              ),
                                            );
                                          }).toList(),
                                        ],
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Text(
                            '\$${widget.cartItem.itemPrice.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 18.0,
                              fontWeight: FontWeight.bold,
                              color: Colors.deepOrange,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    ).animate(
      delay: Duration(milliseconds: 100 * widget.index),
    ).fadeIn(
      duration: const Duration(milliseconds: 600),
    ).slideX(
      begin: 0.2,
      end: 0,
      curve: Curves.easeOutBack,
      duration: const Duration(milliseconds: 800),
    );
  }
}
