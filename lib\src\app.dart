import 'package:flutter/material.dart';

class QSRApp extends StatelessWidget {
  const QSRApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '<PERSON>ca\'s Chicken QSR',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        fontFamily: 'SofiaSans',
      ),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Chica\'s Chicken'),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Welcome to Chica\'s Chicken',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text('QSR Ordering System'),
            ],
          ),
        ),
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}


