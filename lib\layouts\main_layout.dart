import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
import '../screens/cart_screen.dart';
import '../screens/home_screen.dart';
import '../screens/loyalty_screen.dart';
import '../screens/favorites_screen.dart';
import '../screens/games_hub_screen.dart';

import '../screens/order_type_selection_screen.dart';
import '../widgets/custom_bottom_nav_bar.dart';
import '../widgets/navigation_menu_drawer.dart';
import '../widgets/notification_banner.dart';
import '../services/cart_service.dart'; // Import cart service provider
// Import notification service provider
import '../services/notification_service_local_only.dart'; // Import notification service provider

class MainLayout extends ConsumerStatefulWidget { // Changed to ConsumerStatefulWidget
  final int initialPage;

  const MainLayout({super.key, this.initialPage = 0});

  @override
  ConsumerState<MainLayout> createState() => _MainLayoutState(); // Changed to ConsumerState
}

class _MainLayoutState extends ConsumerState<MainLayout> { // Changed to ConsumerState
  late int _selectedIndex;
  // No longer need to instantiate CartService directly
  // late final CartService _cartService;
  // late final NotificationService _notificationService;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late final PageController _pageController;
  bool _hasProcessedArguments = false;

  late final List<Widget> _pages;


  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialPage;
    _pageController = PageController(initialPage: _selectedIndex);
    // 🚀 Performance: Lazy load pages to reduce initial memory usage
    _pages = [
      const HomeScreen(), // Home/Offers page - load immediately
      const GamesHubScreen(), // Games hub - play and earn rewards
      const OrderTypeSelectionScreen(), // Order type selection page
      CartScreen(cartService: ref.read(cartServiceProvider)), // Pass cartService
      const LoyaltyScreen(), // Loyalty page
    ];
  }

  bool _notificationInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_notificationInitialized) {
      _notificationInitialized = true;
      Future.microtask(() async {
        try {
          final notificationService = ref.read(notificationServiceProvider);
          await notificationService.initialize();
        } catch (e) {
          debugPrint('Notification service initialization failed: $e');
        }
      });
    }
  }

  // 🚀 Performance: Async service initialization
  // Removed unused _initializeServicesAsync

  void _onItemTapped(int index) {
    // All tabs now navigate to their respective screens
    setState(() {
      _selectedIndex = index;
    });
    // Jump to the selected page
    _pageController.jumpToPage(index);
  }

  // 🧹 Performance: Proper disposal to prevent memory leaks
  @override
  void dispose() {
    _pageController.dispose();
    // _notificationService.dispose();
    super.dispose();
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 🚀 Advanced analytics/test features removed for production
        // Favorites button (moved to bottom)
        FloatingActionButton(
          heroTag: "favorites",
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const FavoritesScreen(),
              ),
            );
          },
          backgroundColor: Colors.pink,
          tooltip: 'Favorites',
          child: const Icon(Icons.favorite, color: Colors.white),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
  // Access cart service via Riverpod (used below for badge display)

    // Check for route arguments to set initial page (only once)
    final args = ModalRoute.of(context)?.settings.arguments;
    if (!_hasProcessedArguments && args is Map<String, dynamic> && args['initialPage'] != null) {
      final targetPage = args['initialPage'] as int;
      if (targetPage >= 0 && targetPage < _pages.length) {
        _hasProcessedArguments = true;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _selectedIndex = targetPage;
          });
          _pageController.jumpToPage(targetPage);
        });
      }
    }

    return Scaffold(
      key: _scaffoldKey,
      body: Stack(
        children: [
          // 🚀 Performance: Optimized PageView with caching
          PageView.builder(
            physics: const NeverScrollableScrollPhysics(),
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            itemCount: _pages.length,
            itemBuilder: (context, index) {
              return _pages[index];
            },
          ),
          // Notification banner overlay
          const NotificationBanner(),
        ],
      ),
      endDrawer: const NavigationMenuDrawer(),
      floatingActionButton: _buildFloatingActionButtons(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      bottomNavigationBar: CustomBottomNavBar(
        selectedIndex: _selectedIndex,
        onItemSelected: _onItemTapped,
        cartService: ref.watch(cartServiceProvider), // ✅ Pass cart service for consistent badge display
      ),
    );
  }
}
