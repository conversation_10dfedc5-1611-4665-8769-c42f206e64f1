import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/accessibility_service.dart';
import '../models/menu_item.dart';
import '../widgets/accessibility_widgets.dart';

/// 🍗 WCAG 2.1 AA Compliant Menu Item Card
/// Provides accessible menu item display with:
/// - Proper semantic structure and ARIA labels
/// - Screen reader support with detailed descriptions
/// - Keyboard navigation and focus management
/// - High contrast mode compatibility
/// - Touch target size compliance (44x44 CSS pixels minimum)
/// - Price and availability announcements
class AccessibleMenuCard extends StatefulWidget {
  final MenuItem menuItem;
  final VoidCallback? onTap;
  final VoidCallback? onAddToCart;
  final bool isAvailable;
  final String? allergenInfo;
  final String? nutritionalInfo;

  const AccessibleMenuCard({
    super.key,
    required this.menuItem,
    this.onTap,
    this.onAddToCart,
    this.isAvailable = true,
    this.allergenInfo,
    this.nutritionalInfo,
  });

  @override
  State<AccessibleMenuCard> createState() => _AccessibleMenuCardState();
}

class _AccessibleMenuCardState extends State<AccessibleMenuCard> {
  bool _isFocused = false;
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<AccessibilityService>(
      builder: (context, accessibilityService, child) {
        return _buildAccessibleCard(accessibilityService);
      },
    );
  }

  Widget _buildAccessibleCard(AccessibilityService accessibilityService) {
    final theme = Theme.of(context);
    
    // Build comprehensive semantic label
    String semanticLabel = _buildSemanticLabel();
    
    // Build semantic hint for actions
    String semanticHint = widget.isAvailable 
        ? 'Double tap to view details or add to cart'
        : 'Item currently unavailable';

    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      enabled: widget.isAvailable,
      onTap: widget.isAvailable ? widget.onTap : null,
      child: Focus(
        onFocusChange: (focused) {
          setState(() {
            _isFocused = focused;
          });
          if (focused) {
            accessibilityService.announce(semanticLabel);
          }
        },
        child: MouseRegion(
          onEnter: (_) => setState(() => _isHovered = true),
          onExit: (_) => setState(() => _isHovered = false),
          child: GestureDetector(
            onTap: widget.isAvailable ? widget.onTap : null,
            child: AnimatedContainer(
              duration: Duration(
                milliseconds: accessibilityService.isReduceMotionEnabled ? 0 : 200,
              ),
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              padding: accessibilityService.getRecommendedPadding(),
              decoration: _buildCardDecoration(theme, accessibilityService),
              child: _buildCardContent(theme, accessibilityService),
            ),
          ),
        ),
      ),
    );
  }

  String _buildSemanticLabel() {
    String label = widget.menuItem.name;
    
    // Add price information
    label += ', \$${widget.menuItem.price.toStringAsFixed(2)}';
    
    // Add description if available
    if (widget.menuItem.description.isNotEmpty) {
      label += ', ${widget.menuItem.description}';
    }
    
    // Add availability status
    if (!widget.isAvailable) {
      label += ', currently unavailable';
    }
    
    // Add allergen information if available
    if (widget.allergenInfo != null && widget.allergenInfo!.isNotEmpty) {
      label += ', allergen information: ${widget.allergenInfo}';
    }
    
    // Add nutritional information if available
    if (widget.nutritionalInfo != null && widget.nutritionalInfo!.isNotEmpty) {
      label += ', nutritional information: ${widget.nutritionalInfo}';
    }
    
    return label;
  }

  BoxDecoration _buildCardDecoration(ThemeData theme, AccessibilityService accessibilityService) {
    Color borderColor = Colors.transparent;
    double borderWidth = 0;
    
    if (_isFocused) {
      borderColor = theme.primaryColor;
      borderWidth = 2;
    } else if (accessibilityService.isHighContrastEnabled) {
      borderColor = theme.colorScheme.outline;
      borderWidth = 1;
    }
    
    return BoxDecoration(
      color: widget.isAvailable 
          ? theme.cardColor 
          : theme.cardColor.withValues(alpha: 0.6),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: borderColor,
        width: borderWidth,
      ),
      boxShadow: accessibilityService.isReduceMotionEnabled 
          ? null 
          : [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: _isHovered ? 8 : 4,
                offset: const Offset(0, 2),
              ),
            ],
    );
  }

  Widget _buildCardContent(ThemeData theme, AccessibilityService accessibilityService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Image and basic info row
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Menu item image
            _buildMenuImage(theme, accessibilityService),
            
            const SizedBox(width: 16),
            
            // Item details
            Expanded(
              child: _buildItemDetails(theme, accessibilityService),
            ),
          ],
        ),
        
        // Action buttons row
        const SizedBox(height: 16),
        _buildActionButtons(theme, accessibilityService),
      ],
    );
  }

  Widget _buildMenuImage(ThemeData theme, AccessibilityService accessibilityService) {
    return Semantics(
      image: true,
      label: '${widget.menuItem.name} image',
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: theme.colorScheme.surface,
          border: accessibilityService.isHighContrastEnabled
              ? Border.all(color: theme.colorScheme.outline)
              : null,
        ),
        child: widget.menuItem.imageUrl.isNotEmpty
            ? ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  widget.menuItem.imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPlaceholderImage(theme);
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return _buildPlaceholderImage(theme);
                  },
                ),
              )
            : _buildPlaceholderImage(theme),
      ),
    );
  }

  Widget _buildPlaceholderImage(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        Icons.restaurant,
        size: 32,
        color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
    );
  }

  Widget _buildItemDetails(ThemeData theme, AccessibilityService accessibilityService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Item name
        Semantics(
          header: true,
          child: Text(
            widget.menuItem.name,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 18 * accessibilityService.textScaleFactor,
              color: widget.isAvailable 
                  ? null 
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        
        const SizedBox(height: 4),
        
        // Item description
        if (widget.menuItem.description.isNotEmpty) ...[
          Text(
            widget.menuItem.description,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontSize: 14 * accessibilityService.textScaleFactor,
              color: widget.isAvailable 
                  ? theme.colorScheme.onSurface.withValues(alpha: 0.7)
                  : theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
        ],
        
        // Price
        Semantics(
          label: 'Price: \$${widget.menuItem.price.toStringAsFixed(2)}',
          child: Text(
            '\$${widget.menuItem.price.toStringAsFixed(2)}',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 16 * accessibilityService.textScaleFactor,
              color: widget.isAvailable 
                  ? theme.primaryColor
                  : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ),
        
        // Availability status
        if (!widget.isAvailable) ...[
          const SizedBox(height: 4),
          Semantics(
            liveRegion: true,
            child: Text(
              'Currently Unavailable',
              style: theme.textTheme.bodySmall?.copyWith(
                fontSize: 12 * accessibilityService.textScaleFactor,
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme, AccessibilityService accessibilityService) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // View Details Button
        Expanded(
          child: WCAGButton(
            type: ButtonType.outlined,
            onPressed: widget.isAvailable ? widget.onTap : null,
            semanticLabel: 'View details for ${widget.menuItem.name}',
            child: Text(
              'View Details',
              style: TextStyle(
                fontSize: 14 * accessibilityService.textScaleFactor,
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Add to Cart Button
        Expanded(
          child: WCAGButton(
            onPressed: widget.isAvailable ? () {
              accessibilityService.announce(
                '${widget.menuItem.name} added to cart for \$${widget.menuItem.price.toStringAsFixed(2)}'
              );
              widget.onAddToCart?.call();
            } : null,
            semanticLabel: widget.isAvailable 
                ? 'Add ${widget.menuItem.name} to cart for \$${widget.menuItem.price.toStringAsFixed(2)}'
                : 'Add to cart unavailable, item not available',
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.add_shopping_cart,
                  size: 16,
                  semanticLabel: '',
                ),
                const SizedBox(width: 4),
                Text(
                  'Add to Cart',
                  style: TextStyle(
                    fontSize: 14 * accessibilityService.textScaleFactor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// 🏷️ Accessible Menu Category Header
/// Provides proper heading structure for menu sections
class AccessibleMenuCategoryHeader extends StatelessWidget {
  final String categoryName;
  final String? description;
  final int itemCount;

  const AccessibleMenuCategoryHeader({
    super.key,
    required this.categoryName,
    this.description,
    required this.itemCount,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AccessibilityService>(
      builder: (context, accessibilityService, child) {
        final theme = Theme.of(context);
        
        String semanticLabel = '$categoryName category, $itemCount items';
        if (description != null && description!.isNotEmpty) {
          semanticLabel += ', $description';
        }
        
        return Semantics(
          header: true,
          label: semanticLabel,
          child: Container(
            width: double.infinity,
            padding: accessibilityService.getRecommendedPadding(),
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: accessibilityService.isHighContrastEnabled
                  ? Border.all(color: theme.primaryColor)
                  : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  categoryName.toUpperCase(),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 20 * accessibilityService.textScaleFactor,
                    color: theme.primaryColor,
                  ),
                ),
                if (description != null && description!.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    description!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontSize: 14 * accessibilityService.textScaleFactor,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
                const SizedBox(height: 4),
                Text(
                  '$itemCount ${itemCount == 1 ? 'item' : 'items'}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: 12 * accessibilityService.textScaleFactor,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
