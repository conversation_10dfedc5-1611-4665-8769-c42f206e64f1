import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qsr_app/services/cart_service.dart';
import '../services/language_service.dart';

class CustomBottomNavBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;
  final CartService? cartService;

  const CustomBottomNavBar({
    super.key,
    required this.selectedIndex,
    required this.onItemSelected,
    this.cartService,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageService>(
      builder: (context, languageService, child) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1a3d3d) : Colors.white, // Dark teal for dark mode
        boxShadow: [
          BoxShadow(
            color: isDarkMode 
              ? Colors.black.withValues(alpha: 0.3)
              : Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: ConstrainedBox(
          constraints: const BoxConstraints(minHeight: 60),
          child: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            selectedLabelStyle: const TextStyle(
              fontFamily: 'MontserratBlack',
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontFamily: 'MontserratBlack',
              fontSize: 12,
            ),
            selectedItemColor: isDarkMode 
              ? const Color(0xFFDC2626) // Red accent for dark mode
              : const Color(0xFFFF5C22), // Chica Orange for light mode
            unselectedItemColor: isDarkMode 
              ? Colors.white.withValues(alpha: 0.6) // Dimmed white for dark mode
              : Colors.black54,
            elevation: 0,
            backgroundColor: isDarkMode 
              ? const Color(0xFF1a3d3d) // Dark teal for dark mode
              : Colors.white,
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon: const Icon(Icons.local_offer),
                activeIcon: const Icon(Icons.local_offer, size: 28),
                label: languageService.getTranslatedText('HOME'),
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.sports_esports),
                activeIcon: const Icon(Icons.sports_esports, size: 28),
                label: languageService.getTranslatedText('GAMES'),
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.restaurant_menu),
                activeIcon: const Icon(Icons.restaurant_menu, size: 28),
                label: languageService.getTranslatedText('MENU'),
              ),
              BottomNavigationBarItem(
                icon: _buildCartIcon(false, context),
                activeIcon: _buildCartIcon(true, context),
                label: languageService.getTranslatedText('CART'),
              ),
              BottomNavigationBarItem(
                icon: const Icon(Icons.stars),
                activeIcon: const Icon(Icons.stars, size: 28),
                label: languageService.getTranslatedText('LOYALTY'),
              ),
            ],
            currentIndex: selectedIndex,
            selectedFontSize: 12,
            unselectedFontSize: 12,
            onTap: onItemSelected,
          ),
        ),
      ),
    );
      },
    );
  }

  Widget _buildCartIcon(bool isActive, BuildContext context) {
    final itemCount = cartService?.itemCount ?? 0;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Stack(
      children: [
        Icon(
          Icons.shopping_cart,
          size: isActive ? 28 : 24,
        ),
        if (itemCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: isDarkMode 
                  ? const Color(0xFFDC2626) // Red accent for dark mode
                  : const Color(0xFFFF5C22), // Chica Orange for light mode
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: isDarkMode ? const Color(0xFF1a3d3d) : Colors.white,
                  width: 1,
                ),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                itemCount > 99 ? '99+' : itemCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}
