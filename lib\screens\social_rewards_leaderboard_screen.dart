import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// 🏆 Social Rewards Leaderboard Screen - Show top social media sharers
/// 
/// This screen displays a local leaderboard of users who have shared the most
/// content through the Social Rewards feature. Perfect for encouraging engagement!
/// Designed to be simple and fun for a 10-year-old developer to understand.
class SocialRewardsLeaderboardScreen extends StatefulWidget {
  const SocialRewardsLeaderboardScreen({super.key});

  @override
  State<SocialRewardsLeaderboardScreen> createState() => _SocialRewardsLeaderboardScreenState();
}

class _SocialRewardsLeaderboardScreenState extends State<SocialRewardsLeaderboardScreen> {
  List<LeaderboardEntry> _leaderboard = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLeaderboard();
  }

  /// 📊 Load leaderboard data from local storage
  Future<void> _loadLeaderboard() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final leaderboardJson = prefs.getString('social_rewards_leaderboard') ?? '[]';
      final List<dynamic> leaderboardData = json.decode(leaderboardJson);
      
      setState(() {
        _leaderboard = leaderboardData
            .map((entry) => LeaderboardEntry.fromJson(entry))
            .toList();
        
        // Sort by shares count (highest first)
        _leaderboard.sort((a, b) => b.sharesCount.compareTo(a.sharesCount));
        
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('🚨 Error loading leaderboard: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '🏆 SOCIAL REWARDS LEADERBOARD',
          style: TextStyle(
            fontFamily: 'MontserratBlack',
            fontWeight: FontWeight.w900,
          ),
        ),
        backgroundColor: const Color(0xFFFF5C22),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFFF5C22),
              ),
            )
          : _leaderboard.isEmpty
              ? _buildEmptyState()
              : _buildLeaderboard(),
    );
  }

  /// 📭 Empty state when no one has shared yet
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.emoji_events_outlined,
                size: 60,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'No Social Sharers Yet!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                fontFamily: 'MontserratBlack',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            const Text(
              'Be the first to share your delicious Chica\'s Chicken photos and earn your spot on the leaderboard!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF5C22),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                '📸 Start Sharing Now!',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🏆 Build the leaderboard list
  Widget _buildLeaderboard() {
    return Column(
      children: [
        // 🎯 Header with stats
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFF5C22), Color(0xFFFF8A50)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('👥', 'Total Sharers', _leaderboard.length.toString()),
              _buildStatItem('📸', 'Total Shares', _getTotalShares().toString()),
              _buildStatItem('⭐', 'Points Earned', _getTotalPoints().toString()),
            ],
          ),
        ),
        
        // 📋 Leaderboard list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _leaderboard.length,
            itemBuilder: (context, index) {
              return _buildLeaderboardCard(_leaderboard[index], index + 1);
            },
          ),
        ),
      ],
    );
  }

  /// 📊 Build stat item for header
  Widget _buildStatItem(String emoji, String label, String value) {
    return Column(
      children: [
        Text(
          emoji,
          style: const TextStyle(fontSize: 24),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// 🏅 Build individual leaderboard card
  Widget _buildLeaderboardCard(LeaderboardEntry entry, int rank) {
    String rankEmoji = _getRankEmoji(rank);
    Color cardColor = _getRankColor(rank);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: cardColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: cardColor.withValues(alpha: 0.3)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              rankEmoji,
              style: const TextStyle(fontSize: 24),
            ),
          ),
        ),
        title: Text(
          entry.userName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          'Joined: ${_formatDate(entry.joinDate)}',
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12,
          ),
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${entry.sharesCount} shares',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            Text(
              '${entry.pointsEarned} points',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🏅 Get emoji for rank position
  String _getRankEmoji(int rank) {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return '#$rank';
    }
  }

  /// 🎨 Get color for rank position
  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber; // Gold
      case 2:
        return Colors.grey; // Silver
      case 3:
        return Colors.brown; // Bronze
      default:
        return const Color(0xFFFF5C22); // Chica's orange
    }
  }

  /// 📊 Calculate total shares across all users
  int _getTotalShares() {
    return _leaderboard.fold(0, (sum, entry) => sum + entry.sharesCount);
  }

  /// ⭐ Calculate total points earned across all users
  int _getTotalPoints() {
    return _leaderboard.fold(0, (sum, entry) => sum + entry.pointsEarned);
  }

  /// 📅 Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// 📊 Leaderboard Entry Model - Represents one user's social sharing stats
class LeaderboardEntry {
  final String userName;
  final int sharesCount;
  final int pointsEarned;
  final DateTime joinDate;
  final DateTime lastShareDate;

  LeaderboardEntry({
    required this.userName,
    required this.sharesCount,
    required this.pointsEarned,
    required this.joinDate,
    required this.lastShareDate,
  });

  /// 🔄 Convert from JSON (for loading from storage)
  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) {
    return LeaderboardEntry(
      userName: json['userName'] ?? 'Anonymous User',
      sharesCount: json['sharesCount'] ?? 0,
      pointsEarned: json['pointsEarned'] ?? 0,
      joinDate: DateTime.parse(json['joinDate'] ?? DateTime.now().toIso8601String()),
      lastShareDate: DateTime.parse(json['lastShareDate'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// 💾 Convert to JSON (for saving to storage)
  Map<String, dynamic> toJson() {
    return {
      'userName': userName,
      'sharesCount': sharesCount,
      'pointsEarned': pointsEarned,
      'joinDate': joinDate.toIso8601String(),
      'lastShareDate': lastShareDate.toIso8601String(),
    };
  }
}
