import 'package:flutter/material.dart';
import '../constants/heat_levels.dart';

class HeatLevelSelector extends StatefulWidget {
  final String? selectedHeatLevel;
  final Function(String?) onHeatLevelChanged;

  const HeatLevelSelector({
    super.key,
    this.selectedHeatLevel,
    required this.onHeatLevelChanged,
  });

  @override
  State<HeatLevelSelector> createState() => _HeatLevelSelectorState();
}

class _HeatLevelSelectorState extends State<HeatLevelSelector> {
  String? _selectedLevel;

  @override
  void initState() {
    super.initState();
    _selectedLevel = widget.selectedHeatLevel;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 1),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12), // Adjusted to match image
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.whatshot,
                color: Colors.red[600],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Heat Level',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...HeatLevels.all.map((heatLevel) => _buildHeatLevelOption(heatLevel)),
        ],
      ),
    );
  }

  Widget _buildHeatLevelOption(HeatLevel heatLevel) {
    final isSelected = _selectedLevel == heatLevel.name;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedLevel = heatLevel.name;
          });
          widget.onHeatLevelChanged(heatLevel.name);
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected ? heatLevel.color.withValues(alpha: 0.1) : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(8), // Adjusted to match image
            border: Border.all(
              color: isSelected ? heatLevel.color : Theme.of(context).colorScheme.outline,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // Heat level icon
              Icon(
                heatLevel.icon,
                color: heatLevel.color,
                size: 24,
              ),
              const SizedBox(width: 12),
              
              // Flame rating (moved to left)
              Row(
                children: HeatLevels.buildFlameRating(heatLevel.stars, size: 16), // Adjusted size
              ),
              const SizedBox(width: 12), // Added spacing
              
              // Heat level info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      heatLevel.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? heatLevel.color : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      heatLevel.description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Selection indicator (moved to right)
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? heatLevel.color : Theme.of(context).colorScheme.surface,
                  border: Border.all(
                    color: isSelected ? heatLevel.color : Theme.of(context).colorScheme.outline,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 14,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
