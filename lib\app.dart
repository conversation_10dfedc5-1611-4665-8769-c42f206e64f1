import 'package:flutter/material.dart';
import 'package:qsr_app/screens/home_screen.dart';
import 'package:qsr_app/screens/loading_screen.dart';
// No longer need to import cart_service directly here

class QsrApp extends StatelessWidget {
  const QsrApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Chicas Chicken',
      theme: ThemeData(
        primarySwatch: Colors.orange,
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => const LoadingScreen(),
        '/home': (context) => const HomeScreen(), // HomeScreen will now get CartService via Riverpod
      },
    );
  }
}
