// 🔔 LOCAL-ONLY Notification Service for Chica's Chicken App
// This service handles all notification functionality WITHOUT Firebase
// Perfect for avoiding web compilation errors while keeping full functionality!

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
// Import for ChangeNotifier

import 'package:qsr_app/services/notification_service.dart';

final notificationServiceProvider = ChangeNotifierProvider<LocalOnlyNotificationService>((ref) {
  final service = LocalOnlyNotificationService();
  service.initialize(); // Initialize the service when the provider is created
  return service;
});

class LocalOnlyNotificationService extends ChangeNotifier implements NotificationService {
  // Local notifications only - NO FIREBASE!
  FlutterLocalNotificationsPlugin? _localNotifications;

  // Cross-platform navigation callback
  Function(String)? _onNotificationTap;

  // Stream controllers for notification events
  final StreamController<String> _notificationTapController = StreamController<String>.broadcast();
  Stream<String> get onNotificationTap => _notificationTapController.stream;

  // 🚀 Initialize the notification service (NO FIREBASE!)
  @override
  Future<void> initialize({Function(String)? onNotificationTap}) async {
    try {
      debugPrint('🔔 Initializing LOCAL-ONLY Notification Service...');

      _onNotificationTap = onNotificationTap;

      // Initialize timezone data
      tz_data.initializeTimeZones();

      // Initialize local notifications
      await _initializeLocalNotifications();

      debugPrint('✅ LOCAL-ONLY Notification Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Failed to initialize LOCAL-ONLY Notification Service: $e');
    }
  }

  // 📱 Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    try {
      _localNotifications = FlutterLocalNotificationsPlugin();

      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications!.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Request permissions for Android 13+
      if (!kIsWeb) {
        await _localNotifications!
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();
      }

      debugPrint('✅ Local notifications initialized');
    } catch (e) {
      debugPrint('❌ Local notifications initialization failed: $e');
      rethrow;
    }
  }



  // 📨 Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    try {
      String? payload = response.payload;
      debugPrint('🔔 Local notification tapped with payload: $payload');
      _notificationTapController.add(payload ?? '');
      _onNotificationTap?.call(payload ?? '');
        } catch (e) {
      debugPrint('❌ Error handling notification tap: $e');
    }
  }

  // 📅 Schedule daily feedback notification
  @override
  Future<void> scheduleDailyFeedbackNotification() async {
    try {
      // Cancel any existing notification
      await _localNotifications!.cancel(1);

      // Get current time and set to 6 PM today
      final now = DateTime.now();
      var scheduledTime = DateTime(now.year, now.month, now.day, 18, 0); // 6 PM

      // If 6 PM has passed today, schedule for tomorrow
      if (scheduledTime.isBefore(now)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
      }

      final tz.TZDateTime tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);

      const androidDetails = AndroidNotificationDetails(
        'daily_feedback',
        'Daily Feedback Reminders',
        channelDescription: 'Daily reminders to share feedback and earn rewards',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Schedule the notification
      await _localNotifications!.zonedSchedule(
        1, // Notification ID
        '🌟 Loved your meal?', // Title
        'Share your feedback and earn rewards! Tap to visit your loyalty page.', // Body
        tzScheduledTime,
        notificationDetails,
        payload: '/loyalty', // This tells the app where to go when tapped
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time, // Repeat daily at the same time
      );

      debugPrint('✅ Daily feedback notification scheduled for ${scheduledTime.toString()}');
    } catch (e) {
      debugPrint('❌ Failed to schedule daily feedback notification: $e');
    }
  }

  // 📨 Show a local notification immediately
  @override
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'instant_notifications',
        'Instant Notifications',
        channelDescription: 'Immediate notifications from Chica\'s Chicken',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications!.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000), // Unique ID
        title,
        body,
        notificationDetails,
        payload: payload,
      );
    } catch (e) {
      debugPrint('❌ Failed to show local notification: $e');
    }
  }

  // 🧪 Send a test notification
  @override
  Future<void> sendTestNotification() async {
    await showLocalNotification(
      title: '🧪 Test Notification',
      body: 'This is a test notification from Chica\'s Chicken! Tap to visit loyalty page.',
      payload: '/loyalty',
    );
  }

  // ⚙️ User Preferences

  // Get daily feedback notification preference
  @override
  Future<bool> getDailyFeedbackEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('daily_feedback_enabled') ?? true;
  }

  // Set daily feedback notification preference
  @override
  Future<void> setDailyFeedbackEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('daily_feedback_enabled', enabled);

    if (enabled) {
      await scheduleDailyFeedbackNotification();
    } else {
      await _localNotifications?.cancel(1);
    }

    debugPrint('📱 Daily feedback notifications ${enabled ? 'enabled' : 'disabled'}');
  }

  // Check if notifications are enabled (for notification settings screen)
  @override
  Future<bool> areNotificationsEnabled() async {
    return await getDailyFeedbackEnabled();
  }

  // Set notifications enabled (for notification settings screen)
  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    await setDailyFeedbackEnabled(enabled);
  }

  // Stream getters for notification banner and test screen
  @override
  Stream<Map<String, dynamic>> get notificationStream =>
      _notificationTapController.stream.map((route) => {'route': route});

  @override
  Stream<Map<String, dynamic>> get orderUpdateStream =>
      _notificationTapController.stream.map((route) => {'route': route});

  // WebSocket connection status (always false for local-only)
  @override
  bool get isWebSocketConnected => false;

  // 🧹 Cleanup
  @override
  void dispose() {
    _notificationTapController.close();
    super.dispose();
  }

  @override
  Future<String?> getInitialLink() async => null;

  @override
  Stream<String?> get onLinkStream => const Stream.empty();
}
