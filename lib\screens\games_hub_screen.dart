import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

// Define colors from the HTML style for easy reuse
const Color spiceRed = Color(0xFFB5242A);
const Color sunburstYellow = Color(0xFFF8B133);
const Color pickleGreen = Color(0xFF798D63);
const Color charcoalGray = Color(0xFF333333);
const Color lightGray = Color(0xFFF5F5F5);
const Color darkGray = Color(0xFFa0a0a0);

class GamesHubScreen extends StatelessWidget {
  const GamesHubScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: lightGray,
      appBar: AppBar(
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withAlpha(0),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withAlpha((255 * 0.5).round()),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text('GAMES & REWARDS'),
        backgroundColor: const Color(0xFFFF5C22),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.dark_mode, color: Colors.white),
            onPressed: () {
              // Dark mode toggle functionality can be implemented here
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            _buildDashboard(),
            const SizedBox(height: 64),
            _buildFeaturedGames(),
            const SizedBox(height: 64),
            _buildPhotoChallenge(),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboard() {
    return Card(
      elevation: 4,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'My Dashboard',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: charcoalGray,
              ),
            ),
            const SizedBox(height: 16),
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Accumulated Points',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                    Text(
                      '1,250',
                      style: TextStyle(
                        fontSize: 36,
                        fontWeight: FontWeight.w900,
                        color: sunburstYellow,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Prizes Collected',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                    Text(
                      'Free Fries',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: pickleGreen,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: const LinearProgressIndicator(
                value: 0.75,
                minHeight: 8,
                backgroundColor: lightGray,
                valueColor: AlwaysStoppedAnimation<Color>(sunburstYellow),
              ),
            ),
            const SizedBox(height: 4),
            const Align(
              alignment: Alignment.centerRight,
              child: Text(
                '250 points to next prize!',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedGames() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Featured Games',
          style: TextStyle(
            fontFamily: 'SofiaRoughBlackThree',
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: charcoalGray,
          ),
        ),
        const SizedBox(height: 16),
        _buildGameCard(
          imageUrl: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDHLSjAAiLQ3x5QegCQDc_pI_6jLhluyzItQTCTYlixfuEr7ouRlLFrIWIQKXhNb3j-LogCox24RXHwcG6YJqxErR64kyQsJQqks7UDxDdIfVMB6uCBrWVjuOKGCfKeBDyWdzyo6ZG2DgfbmwRpFC_09A1mMSWIZFKjBPhGcvJtdXeIv9hTr8x0SMsl5hSHW2jSsGw5baRmguTF2Y4VEeRBz9hjD8wqd5PS3tSyPRMBi7elvd0gHNRel1C_dn7EZH0fdpBY_cXDjA',
          title: 'Chicken Catch',
          description: 'Catch as many falling chicken pieces as you can!',
        ),
        const SizedBox(height: 16),
        _buildGameCard(
          imageUrl: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDOzaSXFi9rBONEKPIBA0e6fJhvEAlpn6llnyOATfkBoyHfNB-0AIuajZM3MxwKy-Xtw6D6hfPjwe4z49Oe4iwBBpXwipXFeJ1NTk41953La1kpPwqOBxh7U59hNC1sc6rxwIM9gV4Qmyt5sHv51cKj2UELvEZxWnhgwa4XJhanLIzXmpKR2Y2Qw9MOhdwQiX_i6R2w_16vswd_cSnFvCuIeHGIBate2fFRrcjh6uJ6EMxqFWzWCucISlkKXGTwwBNDEQzghwuO5A',
          title: 'Fry Frenzy',
          description: 'Sort the fries by size before time runs out.',
        ),
        const SizedBox(height: 16),
        _buildGameCard(
          imageUrl: 'https://lh3.googleusercontent.com/aida-public/AB6AXuA9RVDpsuYAp3GvlzrF661-4eKRBLGWUd-CacGLeDWaUGPn9gdBoy053YaBYJ2vcTriTig7Lw0VJZMV8czDKoUqOHKqxHO5Xj0OxYj3EwZtID26bjl-6CxE6NVF5V3wIpl6k0iDSe5WYNlwSRqzHaUIF2BtE4amQKz44ifp6dCf_DS7oqJfYwy_9Ro1FUtMTnb68utGpfrsUTjQfSIovmJH8ixzUGv52ZE1RJXfPvNlutvL4bzwZM0otH00F1GyKUc1MijN-rMA3Q',
          title: 'Burger Stack',
          description: 'Build the tallest, most delicious chicken burger.',
        ),
      ],
    );
  }

  Widget _buildGameCard({
    required String imageUrl,
    required String title,
    required String description,
  }) {
    return Card(
      elevation: 4,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CachedNetworkImage(
            imageUrl: imageUrl,
            height: 200,
            width: double.infinity,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: 200,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    spiceRed.withValues(alpha: 0.1),
                    sunburstYellow.withValues(alpha: 0.1),
                  ],
                ),
              ),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
            errorWidget: (context, url, error) => GestureDetector(
              onTap: () => CachedNetworkImage.evictFromCache(imageUrl),
              child: Container(
                height: 200,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      spiceRed.withValues(alpha: 0.1),
                      sunburstYellow.withValues(alpha: 0.1),
                    ],
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.refresh,
                      color: spiceRed,
                      size: 48,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap to retry',
                      style: TextStyle(
                        color: charcoalGray.withValues(alpha: 0.8),
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: charcoalGray,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: spiceRed,
                    minimumSize: const Size(double.infinity, 80),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () {},
                  child: const Text(
                    'Play Now',
                    style: TextStyle(
                      fontFamily: 'MontserratBlack',
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoChallenge() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'PHOTO CHALLENGE',
          style: TextStyle(
            fontFamily: 'SofiaRoughBlackThree',
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: charcoalGray,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 4,
          shadowColor: Colors.black.withValues(alpha: 0.1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                const Icon(
                  Icons.photo_camera,
                  color: sunburstYellow,
                  size: 64,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Capture the Moment',
                  style: TextStyle(
                    fontFamily: 'MontserratBlack',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: charcoalGray,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Share a photo of your meal and earn bonus points!',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: pickleGreen,
                    minimumSize: const Size(double.infinity, 48),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onPressed: () {},
                  icon: const Icon(Icons.upload_file, color: Colors.white),
                  label: const Text(
                    'Upload Photo/Video',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
