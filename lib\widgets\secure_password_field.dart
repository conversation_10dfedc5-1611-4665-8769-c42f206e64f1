import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/auth_state.dart';

/// 🔒 Secure Password Input Field with Strength Indicator
/// Provides comprehensive password validation and security features
class SecurePasswordField extends StatefulWidget {
  final TextEditingController controller;
  final String? labelText;
  final String? hintText;
  final bool showStrengthIndicator;
  final bool showRequirements;
  final Function(PasswordValidationResult)? onValidationChanged;
  final String? Function(String?)? validator;
  final bool enabled;
  final bool autofocus;
  final TextInputAction? textInputAction;
  final Function(String)? onFieldSubmitted;
  final FocusNode? focusNode;

  const SecurePasswordField({
    super.key,
    required this.controller,
    this.labelText,
    this.hintText,
    this.showStrengthIndicator = true,
    this.showRequirements = true,
    this.onValidationChanged,
    this.validator,
    this.enabled = true,
    this.autofocus = false,
    this.textInputAction,
    this.onFieldSubmitted,
    this.focusNode,
  });

  @override
  State<SecurePasswordField> createState() => _SecurePasswordFieldState();
}

class _SecurePasswordFieldState extends State<SecurePasswordField> {
  bool _obscureText = true;
  bool _hasFocus = false;
  PasswordValidationResult _validationResult = PasswordValidationResult.invalid(errors: []);

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onPasswordChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onPasswordChanged);
    super.dispose();
  }

  void _onPasswordChanged() {
    final password = widget.controller.text;
    final result = _validatePassword(password);
    
    if (result != _validationResult) {
      setState(() {
        _validationResult = result;
      });
      widget.onValidationChanged?.call(result);
    }
  }

  PasswordValidationResult _validatePassword(String password) {
    if (password.isEmpty) {
      return PasswordValidationResult.invalid(errors: ['Password is required']);
    }

    final errors = <String>[];
    final suggestions = <String>[];
    double score = 0.0;

    // Length check
    if (password.length < 8) {
      errors.add('Password must be at least 8 characters long');
    } else {
      score += 0.2;
    }

    // Uppercase check
    if (!password.contains(RegExp(r'[A-Z]'))) {
      errors.add('Password must contain at least one uppercase letter');
      suggestions.add('Add an uppercase letter (A-Z)');
    } else {
      score += 0.2;
    }

    // Lowercase check
    if (!password.contains(RegExp(r'[a-z]'))) {
      errors.add('Password must contain at least one lowercase letter');
      suggestions.add('Add a lowercase letter (a-z)');
    } else {
      score += 0.2;
    }

    // Number check
    if (!password.contains(RegExp(r'[0-9]'))) {
      errors.add('Password must contain at least one number');
      suggestions.add('Add a number (0-9)');
    } else {
      score += 0.2;
    }

    // Special character check
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      errors.add('Password must contain at least one special character');
      suggestions.add('Add a special character (!@#\$%^&*)');
    } else {
      score += 0.2;
    }

    // Additional strength checks
    if (password.length >= 12) score += 0.1;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]{2,}'))) score += 0.1;
    if (!password.toLowerCase().contains('password') && 
        !password.toLowerCase().contains('123456') &&
        !password.toLowerCase().contains('qwerty')) {
      score += 0.1;
    }

    // Determine strength
    PasswordStrength strength;
    if (score < 0.3) {
      strength = PasswordStrength.weak;
    } else if (score < 0.5) {
      strength = PasswordStrength.fair;
    } else if (score < 0.7) {
      strength = PasswordStrength.good;
    } else if (score < 0.9) {
      strength = PasswordStrength.strong;
    } else {
      strength = PasswordStrength.veryStrong;
    }

    if (errors.isEmpty) {
      return PasswordValidationResult.valid(
        strength: strength,
        score: score,
        suggestions: suggestions,
      );
    } else {
      return PasswordValidationResult.invalid(
        errors: errors,
        suggestions: suggestions,
      );
    }
  }

  Color _getStrengthColor() {
    switch (_validationResult.strength) {
      case PasswordStrength.weak:
        return Colors.red;
      case PasswordStrength.fair:
        return Colors.orange;
      case PasswordStrength.good:
        return Colors.yellow[700]!;
      case PasswordStrength.strong:
        return Colors.lightGreen;
      case PasswordStrength.veryStrong:
        return Colors.green;
    }
  }

  String _getStrengthText() {
    switch (_validationResult.strength) {
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.fair:
        return 'Fair';
      case PasswordStrength.good:
        return 'Good';
      case PasswordStrength.strong:
        return 'Strong';
      case PasswordStrength.veryStrong:
        return 'Very Strong';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Focus(
          onFocusChange: (hasFocus) {
            setState(() {
              _hasFocus = hasFocus;
            });
          },
          child: TextFormField(
            controller: widget.controller,
            focusNode: widget.focusNode,
            obscureText: _obscureText,
            enabled: widget.enabled,
            autofocus: widget.autofocus,
            textInputAction: widget.textInputAction,
            onFieldSubmitted: widget.onFieldSubmitted,
            validator: widget.validator,
            inputFormatters: [
              // Prevent paste of weak passwords
              FilteringTextInputFormatter.deny(RegExp(r'password|123456|qwerty', caseSensitive: false)),
            ],
            decoration: InputDecoration(
              labelText: widget.labelText ?? 'Password',
              hintText: widget.hintText ?? 'Enter a strong password',
              prefixIcon: const Icon(Icons.lock_outline),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureText ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() {
                    _obscureText = !_obscureText;
                  });
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Color(0xFFFF5C22),
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 2,
                ),
              ),
            ),
          ),
        ),
        
        if (widget.showStrengthIndicator && widget.controller.text.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildStrengthIndicator(),
        ],
        
        if (widget.showRequirements && (_hasFocus || widget.controller.text.isNotEmpty)) ...[
          const SizedBox(height: 12),
          _buildRequirements(),
        ],
      ],
    );
  }

  Widget _buildStrengthIndicator() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Password Strength: ',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              _getStrengthText(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: _getStrengthColor(),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: _validationResult.score,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(_getStrengthColor()),
        ),
      ],
    );
  }

  Widget _buildRequirements() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Password Requirements:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ..._buildRequirementsList(),
        ],
      ),
    );
  }

  List<Widget> _buildRequirementsList() {
    final password = widget.controller.text;
    final requirements = [
      ('At least 8 characters', password.length >= 8),
      ('One uppercase letter (A-Z)', password.contains(RegExp(r'[A-Z]'))),
      ('One lowercase letter (a-z)', password.contains(RegExp(r'[a-z]'))),
      ('One number (0-9)', password.contains(RegExp(r'[0-9]'))),
      ('One special character (!@#\$%^&*)', password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))),
    ];

    return requirements.map((req) {
      final text = req.$1;
      final met = req.$2;
      
      return Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Row(
          children: [
            Icon(
              met ? Icons.check_circle : Icons.radio_button_unchecked,
              size: 16,
              color: met ? Colors.green : Colors.grey,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                text,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: met ? Colors.green : Colors.grey[600],
                  decoration: met ? TextDecoration.lineThrough : null,
                ),
              ),
            ),
          ],
        ),
      );
    }).toList();
  }
}
