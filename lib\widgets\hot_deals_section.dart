import 'package:flutter/material.dart';
import '../models/menu_item.dart';
import '../models/daily_deal.dart';
import '../services/recommendation_service.dart';
import '../services/cart_service.dart';
import '../widgets/deal_card.dart';
import '../screens/hot_deals_page.dart';
import '../screens/order_type_selection_screen.dart';

class HotDealsSection extends StatefulWidget {
  final CartService cartService;
  final String? seeAllDealsText;
  final ImageProvider? icon;
  final Color? dealNameColor;
  final String? title;
  final TextStyle? titleTextStyle;

  const HotDealsSection({
    super.key,
    required this.cartService,
    this.seeAllDealsText,
    this.icon,
    this.dealNameColor,
    this.title,
    this.titleTextStyle,
  });

  @override
  State<HotDealsSection> createState() => _HotDealsSectionState();
}

class _HotDealsSectionState extends State<HotDealsSection> {
  final RecommendationService _recommendationService = RecommendationService();
  List<MenuItem> _deals = [];
  bool _isLoading = true;
  String? _errorMessage;

  // Daily deals data
  final List<DailyDeal> _dailyDeals = [
    const DailyDeal(
      id: 'tuesday_wings',
      name: 'Bite It! Tuesday',
      description: 'All Chicken Bites 10.00',
      price: 14.99,
      category: 'Wings',
      imageUrl: 'assets/images/MENU/Chicken Bites_OG Bites.png',
      dayOfWeek: 'Tuesday',
      badgeText: 'TUESDAY SPECIAL',
    ),
    const DailyDeal(
      id: 'wednesday_family',
      name: 'WINGS Wednesday',
      description: '50% off 2nd Wings Order',
      price: 29.99,
      category: 'Family Combos',
      imageUrl: 'assets/images/MENU/Whole Wing_OG.jpg',
      dayOfWeek: 'Wednesday',
      badgeText: 'WEDNESDAY DEAL',
    ),
    const DailyDeal(
      id: 'thursday_drinks',
      name: 'Thirsty Thursday',
      description: 'Buy Any Sandwich and clock a free drink',
      price: 0,
      category: 'Drinks',
      imageUrl: 'assets/images/MENU/Sauces.jpg',
      dayOfWeek: 'Thursday',
      badgeText: 'THURSDAY SPECIAL',
    ),
    const DailyDeal(
      id: 'friday_combo',
      name: 'Finna Fix\'n Friday',
      description: 'Free regular side with any sandwich',
      price: 15.99,
      category: 'Sides',
      imageUrl: 'assets/images/MENU/Sandos_OG.jpg',
      dayOfWeek: 'Friday',
      badgeText: 'TGI FRIDAY',
    ),
    const DailyDeal(
      id: 'sunday_funday',
      name: 'Sunday Funday',
      description: '10% off all orders',
      price: 0.00,
      category: 'All Menu',
      imageUrl: 'assets/images/MENU/Crew Packs.jpg',
      dayOfWeek: 'Sunday',
      badgeText: 'SUNDAY SPECIAL',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadDeals();
  }

  Future<void> _loadDeals() async {
    try {
      final deals = await _recommendationService.getHotDeals();
      if (mounted) {
        setState(() {
          _deals = deals;
          _isLoading = false;
          _errorMessage = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Unable to load deals. Please try again later.';
        });
      }
    }
  }

  int _getDayNumber(String day) {
    switch (day.toLowerCase()) {
      case 'monday':
        return 1;
      case 'tuesday':
        return 2;
      case 'wednesday':
        return 3;
      case 'thursday':
        return 4;
      case 'friday':
        return 5;
      case 'saturday':
        return 6;
      case 'sunday':
        return 7;
      default:
        return 0;
    }
  }

  Widget _buildTodaysSpecialCard() {
    final currentDay = DateTime.now().weekday;
    final todayDeal = _dailyDeals.firstWhere(
      (deal) => _getDayNumber(deal.dayOfWeek) == currentDay,
      orElse: () => _dailyDeals[0],
    );
    return DealCard(
      deal: MenuItem(
        id: todayDeal.id,
        name: todayDeal.name,
        description: '${todayDeal.dayOfWeek} Special: ${todayDeal.description}',
        price: todayDeal.price,
        category: todayDeal.category,
        imageUrl: todayDeal.imageUrl,
        isSpecial: true,
        available: true,
      ),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const OrderTypeSelectionScreen(),
          ),
        );
      },
      index: 0,
    );
  }


  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      final bool isMobile = constraints.maxWidth < 600;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title Section with Gradient
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 25),
            padding: const EdgeInsets.fromLTRB(24, 32, 24, 40),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: Theme.of(context).brightness == Brightness.dark
                    ? [
                        const Color(0xFF1a3d3d).withValues(alpha: 0.9),
                        const Color(0xFF0d2626).withValues(alpha: 0.8),
                      ]
                    : [
                        Colors.white.withValues(alpha: 0.8),
                        Colors.white.withValues(alpha: 0.5),
                      ],
              ),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Colors.grey.withValues(alpha: 0.0),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (widget.icon != null)
                            Padding(
                              padding: const EdgeInsets.only(right: 16.0),
                              child: ImageIcon(
                                widget.icon!,
                                color: const Color.fromRGBO(255, 107, 53, 1),
                                size: 35,
                              ),
                            ),
                          Text(
                            widget.title ?? 'DEAL WITH IT!',
                            style: widget.titleTextStyle ??
                                Theme.of(context).textTheme.titleLarge?.copyWith(
                                      fontSize: isMobile ? 45 : 45,
                                      fontWeight: FontWeight.w900,
                                      fontFamily: 'SofiaRoughBlackThree',
                                      color: const Color.fromARGB(255, 135, 136, 90),
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 20),
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => HotDealsPage(
                            dailyDeals: _dailyDeals,
                            regularDeals: _deals,
                            cartService: widget.cartService,
                          ),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color.fromRGBO(255, 193, 7, 1),
                      foregroundColor: const Color.fromARGB(255, 255, 255, 255),
                      padding: EdgeInsets.symmetric(
                          horizontal: isMobile ? 75 : 100, vertical: 20),
                      textStyle: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          'assets/images/ICONS/CC-Whole Chicken_White.png',
                          width: isMobile ? 45 : 45,
                          height: isMobile ? 45 : 45,
                        ),
                        const SizedBox(width: 15),
                        Text(
                          widget.seeAllDealsText ?? 'See All Deals',
                          style: TextStyle(
                            fontSize: isMobile ? 25 : 25,
                            fontFamily: 'MontserratBlack',
                            fontWeight: FontWeight.w900,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(40.0),
                child: CircularProgressIndicator(),
              ),
            )
          else if (_errorMessage != null)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _errorMessage!,
                      style: TextStyle(color: Colors.red[700]),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadDeals,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else
            // Combined Deals and Today's Special in horizontal scroll
            SizedBox(
              height: 525, // Increased height for the deal cards
              child: MouseRegion(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  scrollDirection: Axis.horizontal,
                  itemCount: _deals.length + 1, // +1 for today's special
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      // Today's Special as the first item
                      return SizedBox(
                        width: 300, // Fixed width for each card
                        child: Padding(
                          padding: const EdgeInsets.only(right: 16),
                          child: _buildTodaysSpecialCard(),
                        ),
                      );
                    } else {
                      // Regular deals
                      final dealIndex = index - 1;
                      return SizedBox(
                        width: 300, // Fixed width for each card
                        child: Padding(
                          padding: const EdgeInsets.only(right: 16),
                          child: DealCard(
                            deal: _deals[dealIndex],
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const OrderTypeSelectionScreen(),
                                ),
                              );
                            },
                            index: dealIndex,
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
            ),
        ],
      );
    });
  }
}