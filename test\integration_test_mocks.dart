import 'package:mockito/mockito.dart';
import 'package:qsr_app/services/notification_service.dart';
 import 'package:qsr_app/services/theme_service.dart' show ThemeService, AppThemeMode; // Import ThemeService and AppThemeMode

import 'dart:async';

// Manually mock NotificationService
class MockNotificationService extends Mock implements NotificationService {
  @override
  Stream<Map<String, dynamic>> get notificationStream =>
      super.noSuchMethod(Invocation.getter(#notificationStream),
          returnValue: const Stream.empty());

  @override
  Stream<Map<String, dynamic>> get orderUpdateStream =>
      super.noSuchMethod(Invocation.getter(#orderUpdateStream),
          returnValue: const Stream.empty());

  @override
  bool get isWebSocketConnected => super.noSuchMethod(
        Invocation.getter(#isWebSocketConnected),
        returnValue: false,
      );

  @override
  Stream<String?> get onLinkStream =>
      super.noSuchMethod(Invocation.getter(#onLinkStream),
          returnValue: const Stream.empty());

  @override
  Future<void> initialize({Function(String)? onNotificationTap}) =>
      super.noSuchMethod(
        Invocation.method(#initialize, [], {#onNotificationTap: onNotificationTap}),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );

  @override
  Future<void> showLocalNotification({
    required String? title,
    required String? body,
    String? payload,
  }) =>
      super.noSuchMethod(
        Invocation.method(#showLocalNotification, [], {
          #title: title,
          #body: body,
          #payload: payload,
        }),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );

  @override
  Future<void> scheduleDailyFeedbackNotification() => super.noSuchMethod(
        Invocation.method(#scheduleDailyFeedbackNotification, []),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );

  @override
  Future<void> sendTestNotification() => super.noSuchMethod(
        Invocation.method(#sendTestNotification, []),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );

  @override
  Future<bool> getDailyFeedbackEnabled() => super.noSuchMethod(
        Invocation.method(#getDailyFeedbackEnabled, []),
        returnValue: Future.value(false),
      );

  @override
  Future<void> setDailyFeedbackEnabled(bool? enabled) => super.noSuchMethod(
        Invocation.method(#setDailyFeedbackEnabled, [enabled]),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );

  @override
  Future<bool> areNotificationsEnabled() => super.noSuchMethod(
        Invocation.method(#areNotificationsEnabled, []),
        returnValue: Future.value(false),
      );

  @override
  Future<void> setNotificationsEnabled(bool? enabled) => super.noSuchMethod(
        Invocation.method(#setNotificationsEnabled, [enabled]),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(#dispose, []),
        returnValueForMissingStub: null,
      );

  @override
  Future<String?> getInitialLink() => super.noSuchMethod(
        Invocation.method(#getInitialLink, []),
        returnValue: Future.value(),
      );
}

// Manually mock ThemeService
class MockThemeService extends Mock implements ThemeService {
  @override
  Future<void> initialize() => super.noSuchMethod(
        Invocation.method(#initialize, []),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );

  @override
  AppThemeMode get themeMode => super.noSuchMethod(
        Invocation.getter(#themeMode),
        returnValue: AppThemeMode.system, // Default mock value
      );

  @override
  Future<void> toggleTheme() => super.noSuchMethod(
        Invocation.method(#toggleTheme, []),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );

  @override
  Future<void> setThemeMode(AppThemeMode? mode) => super.noSuchMethod(
        Invocation.method(#setThemeMode, [mode]),
        returnValue: Future.value(),
        returnValueForMissingStub: Future.value(),
      );
}