import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:provider/provider.dart' as provider;
import 'package:firebase_core/firebase_core.dart';

// CONFIG
import 'firebase_options.dart';

// WIDGETS
import 'widgets/auth_wrapper.dart';

// SCREENS
import 'screens/loading_screen.dart';
import 'screens/login_screen.dart';
import 'screens/signup_screen.dart';
import 'screens/menu_screen.dart';
import 'screens/cart_screen.dart';
import 'screens/checkout_screen.dart';
import 'layouts/main_layout.dart';

// SERVICES
import 'services/cart_service.dart';
import 'services/language_service.dart';
import 'services/mock_auth_service.dart';
import 'services/notification_service_local_only.dart';
import 'services/navigation_service.dart';
import 'services/theme_service.dart';

// THEMES
import 'themes/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  final themeService = ThemeService();
  await themeService.initialize();
  runApp(ProviderScope(child: MyApp(themeService: themeService)));
}

class MyApp extends StatelessWidget {
  final ThemeService themeService;
  const MyApp({super.key, required this.themeService});

  @override
  Widget build(BuildContext context) {
      return provider.MultiProvider(
        providers: [
          provider.ChangeNotifierProvider(create: (_) => CartService()),
          provider.ChangeNotifierProvider(create: (_) => LanguageService()),
          provider.ChangeNotifierProvider(create: (_) => themeService),
          provider.Provider(create: (_) => MockAuthService()),
          provider.Provider(create: (_) => LocalOnlyNotificationService()),
        ],
        child: provider.Consumer<ThemeService>(builder: (context, themeProvider, child) {
          return Builder(
            builder: (context) {
              return MaterialApp(
                title: 'Chicas Chicken',
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: _getMaterialThemeMode(themeProvider.themeMode),
                navigatorKey: NavigationService.navigatorKey,
                initialRoute: '/',
                routes: {
                  '/': (context) => const AuthWrapper(),
                  '/home': (context) => const MainLayout(),
                  '/loading': (context) => const LoadingScreen(),
                  '/login': (context) => const LoginScreen(),
                  '/signup': (context) => const SignupScreen(),
                  '/menu': (context) => MenuScreen(cartService: provider.Provider.of<CartService>(context, listen: false)),
                  '/cart': (context) => CartScreen(cartService: provider.Provider.of<CartService>(context, listen: false)),
                  '/checkout': (context) => CheckoutScreen(cart: provider.Provider.of<CartService>(context, listen: false).cart),
                },
            );
          },
        );
      }),
    );
  }

  ThemeMode _getMaterialThemeMode(AppThemeMode appThemeMode) {
    switch (appThemeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }
}
