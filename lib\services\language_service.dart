import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/language.dart';

class LanguageService extends ChangeNotifier {
  Language _language = Language.english;
  
  Language get language => _language;
  
  String getLanguageDisplayName(Language lang) {
    switch (lang) {
      case Language.english:
        return 'English';
      case Language.spanish:
        return 'Español';
      default:
        return 'English';
    }
  }
  
  void setLanguage(Language newLanguage) {
    _language = newLanguage;
    notifyListeners();
    _saveLanguagePreference();
  }
  
  Future<void> _saveLanguagePreference() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', _language.toString());
  }
}
