import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/language_service.dart';

class LanguageSelector extends ConsumerWidget {
  final Function(Language)? onLanguageChanged;

  const LanguageSelector({
    super.key,
    this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final languageService = ref.watch(languageServiceProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          languageService.getTranslatedText('LANGUAGE'),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        ...Language.values.map((language) => RadioListTile<Language>(
              title: Text(languageService.getLanguageDisplayName(language)),
              value: language,
              groupValue: languageService.language,
              onChanged: (Language? value) {
                if (value != null) {
                  languageService.setLanguage(value);
                  onLanguageChanged?.call(value);
                }
              },
              activeColor: Theme.of(context).colorScheme.primary,
            )),
      ],
    );
  }
}
