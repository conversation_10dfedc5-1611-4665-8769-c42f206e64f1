import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../constants/colors.dart';
import '../constants/typography.dart';

class GlobalFooter extends StatelessWidget {
  const GlobalFooter({super.key});

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      // Handle error - could show a snackbar or toast
      debugPrint('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).scaffoldBackgroundColor,
            Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.95),
          ],
        ),
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Logo and tagline
          Column(
            children: [
              // Chica's Chicken logo image
              Image.asset(
                'assets/images/ICONS/CHICAS-CHICKEN-Logo.png',
                height: 140, // Adjust height as needed
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to text if image fails to load
                  return Column(
                    children: [
                      Text(
                        "CHICA'S",
                        style: AppTypography.displayMedium.copyWith(
                          color: AppColors.chicaOrange,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 2.0,
                        ),
                      ),
                      Text(
                        "CHICKEN",
                        style: AppTypography.displayMedium.copyWith(
                          color: AppColors.chicaOrange,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 2.0,
                        ),
                      ),
                    ],
                  );
                },
              ),
              const SizedBox(height: 16),
              Text(
                "Blastin' Palettes & Crush'n Pieholes since 2018.",
                style: AppTypography.bodyMedium.copyWith(
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Social media icons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildSocialIcon(
                context,
                Icons.facebook,
                'https://www.facebook.com/chicaschicken',
                'Facebook',
              ),
              const SizedBox(width: 24),
              _buildSocialIcon(
                context,
                Icons.camera_alt, // Instagram icon alternative
                'https://www.instagram.com/chicas.chicken/',
                'Instagram',
              ),
              const SizedBox(width: 24),
              _buildSocialIcon(
                context,
                Icons.restaurant, // Michelin guide icon alternative
                'https://guide.michelin.com/ca/en/article/features/the-bib-gourmand',
                'Michelin Guide',
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Copyright and legal
          Column(
            children: [
              Text(
                '© ${DateTime.now().year} Chica\'s Chicken. All rights reserved.',
                style: AppTypography.bodySmall.copyWith(
                  color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Made with ❤️ in Toronto, Canada',
                style: AppTypography.bodySmall.copyWith(
                  color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSocialIcon(
    BuildContext context,
    IconData icon,
    String url,
    String tooltip,
  ) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: () => _launchUrl(url),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Icon(
            icon,
            color: AppColors.chicaOrange,
            size: 28,
          ),
        ),
      ),
    );
  }
}
