import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/accessibility_service.dart';
import '../widgets/accessibility_widgets.dart';
import '../utils/color_contrast_validator.dart';

/// 🌟 Accessibility Features Example for CHICA'S Chicken
/// Demonstrates how to use the implemented accessibility features
class AccessibilityExampleScreen extends StatefulWidget {
  const AccessibilityExampleScreen({super.key});

  @override
  State<AccessibilityExampleScreen> createState() => _AccessibilityExampleScreenState();
}

class _AccessibilityExampleScreenState extends State<AccessibilityExampleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _nameController = TextEditingController();
  
  bool _isLoading = false;
  String? _emailError;

  @override
  Widget build(BuildContext context) {
    return Consumer<AccessibilityService>(
      builder: (context, accessibilityService, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Accessibility Features Demo'),
            backgroundColor: Theme.of(context).primaryColor,
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: accessibilityService.getRecommendedPadding(),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header with semantic structure
                    _buildHeader(accessibilityService),
                    
                    const SizedBox(height: 24),
                    
                    // Accessibility settings section
                    _buildAccessibilitySettings(accessibilityService),
                    
                    const SizedBox(height: 24),
                    
                    // Form examples
                    _buildFormExamples(accessibilityService),
                    
                    const SizedBox(height: 24),
                    
                    // Button examples
                    _buildButtonExamples(accessibilityService),
                    
                    const SizedBox(height: 24),
                    
                    // Color contrast examples
                    _buildColorContrastExamples(accessibilityService),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(AccessibilityService accessibilityService) {
    return Semantics(
      header: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Accessibility Features',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 28 * accessibilityService.textScaleFactor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This screen demonstrates WCAG 2.1 AA compliant accessibility features implemented in the CHICA\'S Chicken app.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: 16 * accessibilityService.textScaleFactor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccessibilitySettings(AccessibilityService accessibilityService) {
    return Card(
      child: Padding(
        padding: accessibilityService.getRecommendedPadding(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Semantics(
              header: true,
              child: Text(
                'Accessibility Settings',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 20 * accessibilityService.textScaleFactor,
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Screen reader status
            _buildStatusItem(
              'Screen Reader',
              accessibilityService.isScreenReaderEnabled ? 'Enabled' : 'Disabled',
              accessibilityService.isScreenReaderEnabled ? Colors.green : Colors.grey,
              accessibilityService,
            ),
            
            // High contrast status
            _buildStatusItem(
              'High Contrast',
              accessibilityService.isHighContrastEnabled ? 'Enabled' : 'Disabled',
              accessibilityService.isHighContrastEnabled ? Colors.green : Colors.grey,
              accessibilityService,
            ),
            
            // Text scaling
            _buildStatusItem(
              'Text Scale Factor',
              '${(accessibilityService.textScaleFactor * 100).round()}%',
              accessibilityService.textScaleFactor > 1.0 ? Colors.blue : Colors.grey,
              accessibilityService,
            ),
            
            const SizedBox(height: 16),
            
            // TTS toggle
            WCAGButton(
              onPressed: () {
                accessibilityService.toggleTts();
                accessibilityService.announce(
                  accessibilityService.isTtsEnabled 
                    ? 'Text-to-speech enabled' 
                    : 'Text-to-speech disabled'
                );
              },
              semanticLabel: accessibilityService.isTtsEnabled 
                ? 'Disable text-to-speech' 
                : 'Enable text-to-speech',
              child: Text(
                accessibilityService.isTtsEnabled ? 'Disable TTS' : 'Enable TTS',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(
    String label, 
    String value, 
    Color color, 
    AccessibilityService accessibilityService
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16 * accessibilityService.textScaleFactor,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 51),
              borderRadius: BorderRadius.circular(12),
              border: accessibilityService.isHighContrastEnabled
                  ? Border.all(color: color)
                  : null,
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 14 * accessibilityService.textScaleFactor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormExamples(AccessibilityService accessibilityService) {
    return Card(
      child: Padding(
        padding: accessibilityService.getRecommendedPadding(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Semantics(
              header: true,
              child: Text(
                'Accessible Form Examples',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 20 * accessibilityService.textScaleFactor,
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Name field
            AccessibleText(
              'Name *',
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                fontSize: 16 * accessibilityService.textScaleFactor,
              ),
            ),
            const SizedBox(height: 8),
            Semantics(
              label: 'Name, required field',
              textField: true,
              child: TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  hintText: 'Enter your name',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: accessibilityService.getRecommendedPadding(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Name is required';
                  }
                  return null;
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Email field with error example
            AccessibleText(
              'Email Address *',
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                fontSize: 16 * accessibilityService.textScaleFactor,
              ),
            ),
            const SizedBox(height: 8),
            Semantics(
              label: 'Email address, required field',
              textField: true,
              child: TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: InputDecoration(
                  hintText: 'Enter your email address',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: accessibilityService.getRecommendedPadding(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Email is required';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                    return 'Please enter a valid email address';
                  }
                  return null;
                },
              ),
            ),
            
            if (_emailError != null) ...[
              const SizedBox(height: 4),
              Semantics(
                liveRegion: true,
                child: Text(
                  _emailError!,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                    fontSize: 14 * accessibilityService.textScaleFactor,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildButtonExamples(AccessibilityService accessibilityService) {
    return Card(
      child: Padding(
        padding: accessibilityService.getRecommendedPadding(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Semantics(
              header: true,
              child: Text(
                'Accessible Button Examples',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 20 * accessibilityService.textScaleFactor,
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Primary button
            WCAGButton(
              onPressed: () {
                accessibilityService.announce('Primary button pressed');
              },
              semanticLabel: 'Primary action button',
              tooltip: 'This is a primary button with proper accessibility',
              child: const Text('Primary Button'),
            ),
            
            const SizedBox(height: 12),
            
            // Secondary button
            WCAGButton(
              type: ButtonType.outlined,
              onPressed: () {
                accessibilityService.announce('Secondary button pressed');
              },
              semanticLabel: 'Secondary action button',
              child: const Text('Secondary Button'),
            ),
            
            const SizedBox(height: 12),
            
            // Text button
            WCAGButton(
              type: ButtonType.text,
              onPressed: () {
                accessibilityService.announce('Text button pressed');
              },
              semanticLabel: 'Text button for less important actions',
              child: const Text('Text Button'),
            ),
            
            const SizedBox(height: 12),
            
            // Loading button
            WCAGButton(
              isLoading: _isLoading,
              onPressed: _isLoading ? null : () {
                setState(() {
                  _isLoading = true;
                });
                accessibilityService.announce('Loading started');
                
                // Simulate loading
                Future.delayed(const Duration(seconds: 2), () {
                  if (mounted) {
                    setState(() {
                      _isLoading = false;
                    });
                    accessibilityService.announce('Loading completed');
                  }
                });
              },
              semanticLabel: _isLoading ? 'Loading, please wait' : 'Start loading process',
              child: Text(_isLoading ? 'Loading...' : 'Loading Button'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorContrastExamples(AccessibilityService accessibilityService) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: accessibilityService.getRecommendedPadding(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Semantics(
              header: true,
              child: Text(
                'Color Contrast Examples',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 20 * accessibilityService.textScaleFactor,
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Good contrast example
            _buildContrastExample(
              'Good Contrast (WCAG AA)',
              Colors.white,
              theme.primaryColor,
              accessibilityService,
            ),
            
            const SizedBox(height: 12),
            
            // Poor contrast example (for demonstration)
            _buildContrastExample(
              'Poor Contrast (Fails WCAG)',
              Colors.grey[300]!,
              Colors.grey[400]!,
              accessibilityService,
            ),
            
            const SizedBox(height: 12),
            
            // High contrast example
            _buildContrastExample(
              'High Contrast',
              Colors.black,
              Colors.white,
              accessibilityService,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContrastExample(
    String label,
    Color backgroundColor,
    Color textColor,
    AccessibilityService accessibilityService,
  ) {
    final contrastRatio = ColorContrastValidator.calculateContrastRatio(textColor, backgroundColor);
    final meetsWCAG = ColorContrastValidator.meetsWCAGAA(textColor, backgroundColor);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: accessibilityService.isHighContrastEnabled 
            ? Colors.black 
            : Colors.grey[300]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 16 * accessibilityService.textScaleFactor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Contrast Ratio: ${contrastRatio.toStringAsFixed(2)}:1',
            style: TextStyle(
              color: textColor,
              fontSize: 14 * accessibilityService.textScaleFactor,
            ),
          ),
          Text(
            'WCAG AA: ${meetsWCAG ? 'Pass' : 'Fail'}',
            style: TextStyle(
              color: textColor,
              fontSize: 14 * accessibilityService.textScaleFactor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _nameController.dispose();
    super.dispose();
  }
}
