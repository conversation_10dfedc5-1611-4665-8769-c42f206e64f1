import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/mock_auth_service.dart';
import '../screens/login_screen.dart';

/// 🔐 Authentication Required Wrapper
/// Shows login prompt for features that require authentication
/// Allows users to browse the app freely but prompts login for personal features
class AuthRequiredWrapper extends StatelessWidget {
  final Widget child;
  final String featureName;
  final String? message;

  const AuthRequiredWrapper({
    super.key,
    required this.child,
    required this.featureName,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<MockAuthService>(
      builder: (context, authService, _) {
        // If user is authenticated, show the requested feature
        if (authService.isAuthenticated) {
          return child;
        }

        // If not authenticated, show login prompt
        return _buildLoginPrompt(context);
      },
    );
  }

  Widget _buildLoginPrompt(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(featureName),
        backgroundColor: const Color(0xFFFF5C22),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: const Color(0xFFFF5C22).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.account_circle,
                  size: 60,
                  color: Color(0xFFFF5C22),
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Title
              Text(
                'Sign In Required',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Message
              Text(
                message ?? 'Please sign in to access $featureName and save your preferences.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // Benefits
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(Icons.star, color: Colors.orange[600], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Benefits of signing in:',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildBenefit(context, Icons.favorite, 'Save your favorite items'),
                    _buildBenefit(context, Icons.history, 'View order history'),
                    _buildBenefit(context, Icons.card_giftcard, 'Earn reward points'),
                    _buildBenefit(context, Icons.local_offer, 'Get exclusive offers'),
                    _buildBenefit(context, Icons.speed, 'Faster checkout'),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Sign In Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LoginScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF5C22),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    'SIGN IN',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Create Account Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/signup');
                  },
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    side: const BorderSide(color: Color(0xFFFF5C22)),
                    foregroundColor: const Color(0xFFFF5C22),
                  ),
                  child: const Text(
                    'CREATE ACCOUNT',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Continue Browsing
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'Continue browsing without signing in',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBenefit(BuildContext context, IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 🔒 Simple Authentication Check
/// Returns true if user is authenticated, false otherwise
class AuthCheck {
  static bool isAuthenticated(BuildContext context) {
    try {
      final authService = Provider.of<MockAuthService>(context, listen: false);
      return authService.isAuthenticated;
    } catch (e) {
      return false;
    }
  }

  static MockAuthService? getAuthService(BuildContext context) {
    try {
      return Provider.of<MockAuthService>(context, listen: false);
    } catch (e) {
      return null;
    }
  }
}
