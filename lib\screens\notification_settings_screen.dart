// 🔔 Notification Settings Screen
// This screen lets users control their notification preferences
// Think of it as the control panel for notifications!

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/notification_service.dart';

/// 🔔 Notification Settings Screen
/// Allows users to control their notification preferences
class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  late final NotificationService _notificationService;
  bool _notificationsEnabled = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _notificationService = Provider.of<NotificationService>(context, listen: false);
    _loadSettings();
  }

  /// 📊 Load current notification settings
  Future<void> _loadSettings() async {
    try {
      final enabled = await _notificationService.areNotificationsEnabled();
      setState(() {
        _notificationsEnabled = enabled;
        _isLoading = false;
      });
    } catch (error) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Failed to load settings');
    }
  }

  /// 💾 Save notification settings
  Future<void> _saveSettings(bool enabled) async {
    try {
      await _notificationService.setNotificationsEnabled(enabled);
      setState(() => _notificationsEnabled = enabled);
      
      _showSuccessSnackBar(enabled 
        ? 'Notifications enabled! You\'ll receive daily feedback reminders at 6 PM.'
        : 'Notifications disabled.');
    } catch (error) {
      _showErrorSnackBar('Failed to save settings');
    }
  }

  /// 💬 Show a success snack bar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// ⚠️ Show an error snack bar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                SwitchListTile(
                  title: const Text('Enable Notifications'),
                  value: _notificationsEnabled,
                  onChanged: (bool value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                    _saveSettings(value);
                  },
                ),
                // Add more notification settings here if needed
              ],
            ),
    );
  }
}
