/// 🔐 Authentication State Management
/// Handles different states of user authentication and provides type-safe state management
library;

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Authentication State Model
class AuthState {
  final AuthStatus status;
  final String? userId;
  final String? email;
  final String? displayName;
  final String? photoUrl;
  final bool emailVerified;
  final String? errorMessage;
  final String? errorCode;
  final bool isLoading;
  final DateTime? lastActivity;

  const AuthState({
    required this.status,
    this.userId,
    this.email,
    this.displayName,
    this.photoUrl,
    required this.emailVerified,
    this.errorMessage,
    this.errorCode,
    required this.isLoading,
    this.lastActivity,
  });

  /// Initial state
  factory AuthState.initial() {
    return const AuthState(
      status: AuthStatus.initial,
      emailVerified: false,
      isLoading: false,
    );
  }

  /// Loading state
  factory AuthState.loading() {
    return const AuthState(
      status: AuthStatus.loading,
      emailVerified: false,
      isLoading: true,
    );
  }

  /// Authenticated state
  factory AuthState.authenticated({
    required String userId,
    required String email,
    String? displayName,
    String? photoUrl,
    required bool emailVerified,
  }) {
    return AuthState(
      status: AuthStatus.authenticated,
      userId: userId,
      email: email,
      displayName: displayName,
      photoUrl: photoUrl,
      emailVerified: emailVerified,
      isLoading: false,
      lastActivity: DateTime.now(),
    );
  }


  /// Unauthenticated state
  factory AuthState.unauthenticated() {
    return const AuthState(
      status: AuthStatus.unauthenticated,
      emailVerified: false,
      isLoading: false,
    );
  }

  /// Error state
  factory AuthState.error({
    required String message,
    String? code,
  }) {
    return AuthState(
      status: AuthStatus.error,
      emailVerified: false,
      isLoading: false,
      errorMessage: message,
      errorCode: code,
    );
  }

  /// Copy with new values
  AuthState copyWith({
    AuthStatus? status,
    String? userId,
    String? email,
    String? displayName,
    String? photoUrl,
    bool? emailVerified,
    String? errorMessage,
    String? errorCode,
    bool? isLoading,
    DateTime? lastActivity,
  }) {
    return AuthState(
      status: status ?? this.status,
      userId: userId ?? this.userId,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      emailVerified: emailVerified ?? this.emailVerified,
      errorMessage: errorMessage ?? this.errorMessage,
      errorCode: errorCode ?? this.errorCode,
      isLoading: isLoading ?? this.isLoading,
      lastActivity: lastActivity ?? this.lastActivity,
    );
  }

  /// Check if user is authenticated
  bool get isAuthenticated => status == AuthStatus.authenticated;

  /// Check if there's an error
  bool get hasError => status == AuthStatus.error;

  /// Check if authentication is in progress
  bool get isAuthenticating => status == AuthStatus.loading;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.status == status &&
        other.userId == userId &&
        other.email == email &&
        other.displayName == displayName &&
        other.photoUrl == photoUrl &&
        other.emailVerified == emailVerified &&
        other.errorMessage == errorMessage &&
        other.errorCode == errorCode &&
        other.isLoading == isLoading;
  }

  @override
  int get hashCode {
    return Object.hash(
      status,
      userId,
      email,
      displayName,
      photoUrl,
      emailVerified,
      errorMessage,
      errorCode,
      isLoading,
    );
  }

  @override
  String toString() {
    return 'AuthState(status: $status, userId: $userId, email: $email, '
           'displayName: $displayName, emailVerified: $emailVerified, '
           'isLoading: $isLoading, errorMessage: $errorMessage)';
  }
}

/// Password Strength Enum
enum PasswordStrength {
  weak,
  fair,
  good,
  strong,
  veryStrong,
}

/// Password Validation Result
class PasswordValidationResult {
  final bool isValid;
  final PasswordStrength strength;
  final List<String> errors;
  final List<String> suggestions;
  final double score; // 0.0 to 1.0

  const PasswordValidationResult({
    required this.isValid,
    required this.strength,
    required this.errors,
    required this.suggestions,
    required this.score,
  });

  /// Create a failed validation result
  factory PasswordValidationResult.invalid({
    required List<String> errors,
    List<String>? suggestions,
  }) {
    return PasswordValidationResult(
      isValid: false,
      strength: PasswordStrength.weak,
      errors: errors,
      suggestions: suggestions ?? [],
      score: 0.0,
    );
  }

  /// Create a valid validation result
  factory PasswordValidationResult.valid({
    required PasswordStrength strength,
    required double score,
    List<String>? suggestions,
  }) {
    return PasswordValidationResult(
      isValid: true,
      strength: strength,
      errors: [],
      suggestions: suggestions ?? [],
      score: score,
    );
  }
}

/// Authentication Error Codes
class AuthErrorCodes {
  static const String emailAlreadyInUse = 'email-already-in-use';
  static const String invalidEmail = 'invalid-email';
  static const String operationNotAllowed = 'operation-not-allowed';
  static const String weakPassword = 'weak-password';
  static const String userDisabled = 'user-disabled';
  static const String userNotFound = 'user-not-found';
  static const String wrongPassword = 'wrong-password';
  static const String tooManyRequests = 'too-many-requests';
  static const String networkRequestFailed = 'network-request-failed';
  static const String invalidCredential = 'invalid-credential';
  static const String accountExistsWithDifferentCredential = 'account-exists-with-different-credential';
  static const String requiresRecentLogin = 'requires-recent-login';
  static const String providerAlreadyLinked = 'provider-already-linked';
  static const String noSuchProvider = 'no-such-provider';
  static const String invalidUserToken = 'invalid-user-token';
  static const String userTokenExpired = 'user-token-expired';
  static const String sessionCookieExpired = 'session-cookie-expired';
  static const String insufficientPermission = 'insufficient-permission';
  static const String missingAndroidPkgName = 'missing-android-pkg-name';
  static const String missingContinueUri = 'missing-continue-uri';
  static const String missingIosBundleId = 'missing-ios-bundle-id';
  static const String invalidContinueUri = 'invalid-continue-uri';
  static const String unauthorizedContinueUri = 'unauthorized-continue-uri';
}

/// Authentication Exception
class AuthException implements Exception {
  final String message;
  final String? code;
  final dynamic originalException;

  const AuthException({
    required this.message,
    this.code,
    this.originalException,
  });

  @override
  String toString() {
    return 'AuthException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}

/// Biometric Authentication Result
class BiometricAuthResult {
  final bool success;
  final String? errorMessage;
  final BiometricAuthError? errorType;

  const BiometricAuthResult({
    required this.success,
    this.errorMessage,
    this.errorType,
  });

  factory BiometricAuthResult.success() {
    return const BiometricAuthResult(success: true);
  }

  factory BiometricAuthResult.failure({
    required String message,
    BiometricAuthError? errorType,
  }) {
    return BiometricAuthResult(
      success: false,
      errorMessage: message,
      errorType: errorType,
    );
  }
}

/// Biometric Authentication Error Types
enum BiometricAuthError {
  notAvailable,
  notEnrolled,
  passcodeNotSet,
  cancelled,
  timeout,
  tooManyAttempts,
  unknown,
}
