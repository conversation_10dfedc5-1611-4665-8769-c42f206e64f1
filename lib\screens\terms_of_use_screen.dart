import 'package:flutter/material.dart';
import '../widgets/accessible_text.dart';

class TermsOfUseScreen extends StatelessWidget {
  const TermsOfUseScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Use'),
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: AccessibleText('Terms of Use content will be loaded here'),
      ),
    );
  }
}
