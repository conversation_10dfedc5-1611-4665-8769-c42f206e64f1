@echo off
echo 🔍 ANALYZING CURRENT ERRORS
echo.
flutter analyze --no-fatal-infos > current_errors.txt 2>&1
echo Analysis complete. Checking error types...
echo.

echo 📊 ERROR BREAKDOWN:
findstr /c:"error •" current_errors.txt | find /c "error" > error_count.txt
set /p ERROR_COUNT=<error_count.txt
echo Total errors found: %ERROR_COUNT%
echo.

echo 🔥 CRITICAL ERRORS (excluding Firebase):
findstr /v /c:"firebase" current_errors.txt | findstr /c:"error •"
echo.

echo 📋 Full analysis saved to current_errors.txt
pause